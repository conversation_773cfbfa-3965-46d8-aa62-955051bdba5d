#!/usr/bin/env python3
"""
简化的图像增强服务测试
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional
import cv2
import numpy as np
import os
import logging
import json
import time
import hashlib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="图像增强API", description="基于AI的图像超分辨率增强服务")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

UPLOAD_DIR = "uploads"
PROCESSED_DIR = "uploads/processed"

os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(PROCESSED_DIR, exist_ok=True)

def cleanup_old_files(directory, max_files=50):
    """清理旧文件，保留最新的max_files个文件"""
    try:
        files = []
        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            if os.path.isfile(filepath):
                files.append((filepath, os.path.getmtime(filepath)))

        # 按修改时间排序，最新的在前
        files.sort(key=lambda x: x[1], reverse=True)

        # 删除超出限制的旧文件
        for filepath, _ in files[max_files:]:
            try:
                os.remove(filepath)
                logger.info(f"清理旧文件: {filepath}")
            except Exception as e:
                logger.warning(f"清理文件失败: {filepath}, 错误: {e}")

    except Exception as e:
        logger.warning(f"清理目录失败: {directory}, 错误: {e}")

# 参数模型定义
class EnhanceParams(BaseModel):
    scale: int = 4
    sharpening: float = 0.0
    denoising: int = 0
    saturation: float = 1.0
    contrast: float = 1.0
    brightness: int = 0
    beauty: float = 0.0

def simple_enhance_image(input_path, output_path):
    """简单的图像增强函数"""
    try:
        logger.info(f"开始增强图像: {input_path}")
        
        # 读取图像
        img = cv2.imread(input_path, cv2.IMREAD_COLOR)
        if img is None:
            raise ValueError(f"无法读取图像文件: {input_path}")
        
        # 使用双三次插值进行4倍放大
        height, width = img.shape[:2]
        new_height = int(height * 4)
        new_width = int(width * 4)
        
        enhanced = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)
        
        # 应用锐化滤波器
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(enhanced, -1, kernel)
        
        # 混合原始放大图像和锐化图像
        result = cv2.addWeighted(enhanced, 0.7, sharpened, 0.3, 0)
        
        # 保存结果
        cv2.imwrite(output_path, result)
        
        logger.info(f"图像增强完成: {output_path}")
        
    except Exception as e:
        logger.error(f"图像增强失败: {str(e)}")
        raise e

@app.post("/enhance/")
async def enhance(
    file: UploadFile = File(...),
    params: str = Form(default='{}')
):
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图像文件")

        # 解析参数
        try:
            params_dict = json.loads(params) if params else {}
        except json.JSONDecodeError:
            params_dict = {}

        # 验证和设置默认参数
        enhance_params = EnhanceParams(**params_dict)

        logger.info(f"开始处理图像: {file.filename}")
        logger.info(f"处理参数: {enhance_params.model_dump()}")

        # 生成唯一的输出文件名，基于参数和时间戳
        params_str = json.dumps(enhance_params.model_dump(), sort_keys=True)
        params_hash = hashlib.md5(params_str.encode()).hexdigest()[:8]
        timestamp = int(time.time() * 1000) % 100000  # 取时间戳后5位

        file_name, file_ext = os.path.splitext(file.filename)
        unique_filename = f"enhanced_{file_name}_{params_hash}_{timestamp}{file_ext}"

        input_path = f"{UPLOAD_DIR}/{file.filename}"
        output_path = f"{PROCESSED_DIR}/{unique_filename}"

        # 保存上传的文件
        with open(input_path, "wb") as f:
            content = await file.read()
            f.write(content)

        logger.info(f"文件已保存到: {input_path}")

        # 调用增强的图像处理函数
        from app.utils.enhance import enhance_image
        enhance_image(input_path, output_path, **enhance_params.model_dump())

        logger.info(f"图像增强完成: {output_path}")

        # 清理旧文件
        cleanup_old_files(UPLOAD_DIR, max_files=20)
        cleanup_old_files(PROCESSED_DIR, max_files=30)

        return {
            "filename": file.filename,
            "enhanced_url": unique_filename,
            "message": "图像增强完成",
            "params_used": enhance_params.model_dump()
        }

    except Exception as e:
        logger.error(f"图像增强失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"图像增强失败: {str(e)}")

@app.get("/result/{filename}")
async def get_result(filename: str):
    try:
        file_path = f"{PROCESSED_DIR}/{filename}"
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")
        
        return FileResponse(file_path)
    
    except Exception as e:
        logger.error(f"获取文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件失败: {str(e)}")

@app.get("/presets/")
async def get_presets():
    """获取预设配置"""
    presets = {
        "default": {
            "name": "默认设置",
            "description": "标准的4倍RealESRGAN超分辨率增强",
            "params": {
                "scale": 4,
                "sharpening": 0.0,
                "denoising": 0,
                "saturation": 1.0,
                "contrast": 1.0,
                "brightness": 0,
                "beauty": 0.0
            }
        },
        "portrait": {
            "name": "人像优化",
            "description": "适合人像照片的RealESRGAN增强设置",
            "params": {
                "scale": 4,
                "sharpening": 0.1,
                "denoising": 5,
                "saturation": 1.1,
                "contrast": 1.05,
                "brightness": 5,
                "beauty": 0.3
            }
        },
        "landscape": {
            "name": "风景增强",
            "description": "适合风景照片的RealESRGAN增强设置",
            "params": {
                "scale": 4,
                "sharpening": 0.2,
                "denoising": 3,
                "saturation": 1.2,
                "contrast": 1.1,
                "brightness": 0,
                "beauty": 0.0
            }
        },
        "vintage": {
            "name": "复古风格",
            "description": "营造复古怀旧效果的RealESRGAN增强",
            "params": {
                "scale": 4,
                "sharpening": 0.05,
                "denoising": 8,
                "saturation": 0.8,
                "contrast": 0.9,
                "brightness": -10,
                "beauty": 0.0
            }
        },
        "fast": {
            "name": "快速处理",
            "description": "2倍RealESRGAN增强，处理速度更快",
            "params": {
                "scale": 2,
                "sharpening": 0.1,
                "denoising": 0,
                "saturation": 1.0,
                "contrast": 1.0,
                "brightness": 0,
                "beauty": 0.0
            }
        }
    }
    return {"presets": presets}

@app.get("/")
async def root():
    return {
        "message": "图像增强API服务正在运行",
        "version": "2.0.0",
        "features": [
            "RealESRGAN超分辨率",
            "多种增强参数",
            "预设配置",
            "美颜滤镜",
            "色彩增强",
            "降噪处理"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
