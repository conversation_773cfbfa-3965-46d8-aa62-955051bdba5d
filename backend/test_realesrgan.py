#!/usr/bin/env python3
"""
测试RealESRGAN模型是否正常工作
"""

import sys
import os
sys.path.append('.')

def test_realesrgan():
    """测试RealESRGAN模型加载和基本功能"""
    try:
        print("=== RealESRGAN模型测试 ===")
        
        # 1. 测试模型导入
        print("1. 测试模型导入...")
        from models.sr_model import get_sr_model
        print("✅ 模型导入成功")
        
        # 2. 测试模型加载
        print("2. 测试模型加载...")
        model = get_sr_model(scale=4)
        print("✅ 模型加载成功")
        
        # 3. 测试图像增强函数导入
        print("3. 测试图像增强函数...")
        from app.utils.enhance import enhance_image
        print("✅ 图像增强函数导入成功")
        
        print("\n🎉 所有测试通过！RealESRGAN模型已准备就绪")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_realesrgan()
    sys.exit(0 if success else 1)
