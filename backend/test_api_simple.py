#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import requests
import json

def test_api():
    """测试API基本功能"""
    try:
        print("=== API功能测试 ===")
        
        # 1. 测试基础API
        print("1. 测试基础API...")
        response = requests.get("http://localhost:8001/")
        if response.status_code == 200:
            print("✅ 基础API正常")
            print(f"   响应: {response.json()['message']}")
        else:
            print(f"❌ 基础API失败: {response.status_code}")
            return False
        
        # 2. 测试预设配置API
        print("2. 测试预设配置API...")
        response = requests.get("http://localhost:8001/presets/")
        if response.status_code == 200:
            presets = response.json()
            print("✅ 预设配置API正常")
            print(f"   可用预设: {list(presets['presets'].keys())}")
        else:
            print(f"❌ 预设配置API失败: {response.status_code}")
            return False
        
        # 3. 测试CORS
        print("3. 测试CORS配置...")
        headers = {
            'Origin': 'http://localhost:3001',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options("http://localhost:8001/enhance/", headers=headers)
        if response.status_code == 200:
            print("✅ CORS配置正常")
        else:
            print(f"❌ CORS配置失败: {response.status_code}")
            return False
        
        print("\n🎉 所有API测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_api()
    exit(0 if success else 1)
