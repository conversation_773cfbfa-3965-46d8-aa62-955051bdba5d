from fastapi import FastAPI, File, UploadFile, Form, HTTPException
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from app.utils.enhance import enhance_image
import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="图像增强API", description="基于AI的图像超分辨率增强服务")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

UPLOAD_DIR = "uploads"
PROCESSED_DIR = "uploads/processed"

os.makedirs(UPLOAD_DIR, exist_ok=True)
os.makedirs(PROCESSED_DIR, exist_ok=True)

@app.post("/enhance/")
async def enhance(file: UploadFile = File(...), params: str = Form(None)):
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图像文件")

        logger.info(f"开始处理图像: {file.filename}")

        # 解析参数
        enhance_params = {}
        if params:
            try:
                import json
                enhance_params = json.loads(params)
                logger.info(f"接收到的参数: {enhance_params}")
            except json.JSONDecodeError:
                logger.warning("参数解析失败，使用默认参数")

        # 生成唯一的文件名，避免缓存问题
        import time
        timestamp = int(time.time() * 1000)
        unique_filename = f"{timestamp}_{file.filename}"

        input_path = f"{UPLOAD_DIR}/{unique_filename}"
        output_filename = f"enhanced_{timestamp}_{file.filename}"
        output_path = f"{PROCESSED_DIR}/{output_filename}"

        # 保存上传的文件
        with open(input_path, "wb") as f:
            content = await file.read()
            f.write(content)

        logger.info(f"文件已保存到: {input_path}")

        # 调用 AI 模型处理，传递参数
        enhance_image(input_path, output_path, **enhance_params)

        logger.info(f"图像增强完成: {output_path}")

        return {
            "filename": file.filename,
            "enhanced_url": output_filename,
            "message": "图像增强完成",
            "params_used": enhance_params
        }

    except Exception as e:
        logger.error(f"图像增强失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"图像增强失败: {str(e)}")

@app.get("/result/{filename}")
async def get_result(filename: str):
    try:
        file_path = f"{PROCESSED_DIR}/{filename}"
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        return FileResponse(file_path)

    except Exception as e:
        logger.error(f"获取文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文件失败: {str(e)}")

@app.get("/presets/")
async def get_presets():
    """获取预设配置"""
    presets = {
        "default": {
            "name": "默认设置",
            "params": {
                "scale": 4,
                "sharpening": 0.0,
                "denoising": 0,
                "saturation": 1.0,
                "contrast": 1.0,
                "brightness": 0,
                "beauty": 0.0
            }
        },
        "portrait": {
            "name": "人像优化",
            "params": {
                "scale": 4,
                "sharpening": 0.2,
                "denoising": 5,
                "saturation": 1.1,
                "contrast": 1.05,
                "brightness": 5,
                "beauty": 0.3
            }
        },
        "landscape": {
            "name": "风景增强",
            "params": {
                "scale": 4,
                "sharpening": 0.3,
                "denoising": 3,
                "saturation": 1.2,
                "contrast": 1.1,
                "brightness": 0,
                "beauty": 0.0
            }
        },
        "vintage": {
            "name": "复古风格",
            "params": {
                "scale": 4,
                "sharpening": 0.1,
                "denoising": 8,
                "saturation": 0.8,
                "contrast": 0.9,
                "brightness": -10,
                "beauty": 0.0
            }
        },
        "fast": {
            "name": "快速处理",
            "params": {
                "scale": 2,
                "sharpening": 0.1,
                "denoising": 0,
                "saturation": 1.0,
                "contrast": 1.0,
                "brightness": 0,
                "beauty": 0.0
            }
        }
    }
    return {"presets": presets}

@app.get("/")
async def root():
    return {"message": "图像增强API服务正在运行", "version": "1.0.0"}