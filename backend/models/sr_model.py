import os
import logging
import torch

logger = logging.getLogger(__name__)



class RealESRGANModel:
    """RealESRGAN模型封装类"""

    def __init__(self, model_path, scale=4, device='cpu'):
        self.model_path = model_path
        self.scale = scale
        self.device = device
        self.upsampler = None
        self._load_model()

    def _load_model(self):
        """加载RealESRGAN模型"""
        try:
            from realesrgan import RealESRGANer
            from basicsr.archs.srvgg_arch import SRVGGNetCompact

            # 检查模型文件是否存在
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

            logger.info(f"加载RealESRGAN模型: {self.model_path}")

            # 创建模型架构
            model = SRVGGNetCompact(num_feat=64, num_conv=32, upscale=4, act_type='prelu')

            # 加载模型权重
            model_weights = torch.load(self.model_path, map_location=self.device)
            if 'params' in model_weights:
                model.load_state_dict(model_weights['params'])
            else:
                model.load_state_dict(model_weights)

            model.eval()

            # 创建RealESRGANer实例
            self.upsampler = RealESRGANer(
                scale=self.scale,
                model_path=self.model_path,
                model=model,
                tile=0,
                tile_pad=10,
                pre_pad=0,
                half=False,
                device=self.device
            )

            logger.info("RealESRGAN模型加载成功")

        except Exception as e:
            logger.error(f"RealESRGAN模型加载失败: {str(e)}")
            raise e

    def enhance(self, img, outscale=None):
        """
        使用RealESRGAN进行图像增强
        Args:
            img: 输入图像 (numpy array)
            outscale: 放大倍数
        Returns:
            tuple: (增强后的图像, None)
        """
        if self.upsampler is None:
            raise RuntimeError("模型未正确加载")

        if img is None:
            raise ValueError("输入图像为空")

        try:
            # RealESRGAN处理
            output, _ = self.upsampler.enhance(img, outscale=outscale or self.scale)
            return output, None

        except Exception as e:
            logger.error(f"RealESRGAN增强失败: {str(e)}")
            raise e

# 全局模型实例
sr_model = None

def get_sr_model(scale=4):
    """
    获取RealESRGAN超分辨率模型实例
    Args:
        scale: 放大倍数 (2, 4)
    Returns:
        RealESRGAN模型实例
    Raises:
        FileNotFoundError: 模型文件不存在
        RuntimeError: 模型加载失败
    """
    global sr_model
    if sr_model is None:
        # 只支持RealESRGAN模型
        current_dir = os.path.dirname(os.path.abspath(__file__))
        model_path = os.path.join(current_dir, '..', 'realesr-general-x4v3.pth')

        if not os.path.exists(model_path):
            raise FileNotFoundError(f"RealESRGAN模型文件不存在: {model_path}")

        try:
            logger.info(f"加载RealESRGAN模型: {model_path}")
            sr_model = RealESRGANModel(model_path, scale=scale, device='cpu')
            logger.info("RealESRGAN模型加载成功")
        except Exception as e:
            logger.error(f"RealESRGAN模型加载失败: {str(e)}")
            raise RuntimeError(f"RealESRGAN模型加载失败: {str(e)}")

    return sr_model

def reset_model():
    """重置模型实例，用于切换模型类型"""
    global sr_model
    sr_model = None