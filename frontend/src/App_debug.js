import React from 'react';

function App() {
  return (
    <div style={{ 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>图像增强应用 - 调试版本</h1>
      <p>如果您看到这个页面，说明React应用正在正常运行。</p>
      
      <div style={{
        backgroundColor: '#f0f0f0',
        padding: '15px',
        borderRadius: '5px',
        margin: '20px 0'
      }}>
        <h3>调试信息:</h3>
        <ul>
          <li>React版本: {React.version}</li>
          <li>当前时间: {new Date().toLocaleString()}</li>
          <li>页面URL: {window.location.href}</li>
        </ul>
      </div>
      
      <button 
        onClick={() => alert('按钮点击正常工作！')}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        测试按钮
      </button>
    </div>
  );
}

export default App;
