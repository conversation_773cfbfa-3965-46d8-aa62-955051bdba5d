/**
 * API自动发现功能测试脚本
 * 在浏览器控制台中运行此脚本来测试API发现功能
 */

import { getApiUrl, resetApiConfig } from './config/api';

// 测试API自动发现功能
async function testApiDiscovery() {
  console.log('🧪 开始测试API自动发现功能...');
  
  try {
    // 重置配置以强制重新发现
    resetApiConfig();
    
    // 测试基础API发现
    console.log('1. 测试基础API发现...');
    const baseUrl = await getApiUrl('');
    console.log('✅ 发现的API基础地址:', baseUrl);
    
    // 测试具体端点
    console.log('2. 测试具体端点...');
    const enhanceUrl = await getApiUrl('/enhance/');
    console.log('✅ 增强端点地址:', enhanceUrl);
    
    const presetsUrl = await getApiUrl('/presets/');
    console.log('✅ 预设端点地址:', presetsUrl);
    
    // 验证API响应
    console.log('3. 验证API响应...');
    const response = await fetch(baseUrl);
    const data = await response.json();
    console.log('✅ API响应:', data);
    
    console.log('🎉 API自动发现功能测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ API自动发现功能测试失败:', error);
    return false;
  }
}

// 导出测试函数
window.testApiDiscovery = testApiDiscovery;

console.log('API发现测试脚本已加载，在控制台运行 testApiDiscovery() 来测试');

export { testApiDiscovery };
