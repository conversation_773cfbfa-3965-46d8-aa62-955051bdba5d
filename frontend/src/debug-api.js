// 调试API发现功能
console.log('开始调试API发现功能...');

// 测试URL生成
const currentHost = window.location.hostname;
const currentProtocol = window.location.protocol;
const commonPorts = [8001, 8000, 5000, 3001, 8080];

console.log('当前主机:', currentHost);
console.log('当前协议:', currentProtocol);

const candidates = [];

// 1. 环境变量配置的地址
if (process.env.REACT_APP_API_URL) {
  candidates.push(process.env.REACT_APP_API_URL);
}

// 2. 同主机的常见端口
for (const port of commonPorts) {
  candidates.push(`${currentProtocol}//${currentHost}:${port}`);
}

// 3. localhost的常见端口
if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
  for (const port of commonPorts) {
    candidates.push(`${currentProtocol}//localhost:${port}`);
    candidates.push(`${currentProtocol}//127.0.0.1:${port}`);
  }
}

console.log('候选地址列表:', candidates);

// 测试每个地址
async function testUrls() {
  for (const url of candidates) {
    try {
      console.log(`测试地址: ${url}`);
      const response = await fetch(`${url}/`, {
        method: 'GET',
        headers: { 'Accept': 'application/json' }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ 成功: ${url}`, data);
        if (data && data.message && data.message.includes('图像增强')) {
          console.log(`🎉 找到有效的API服务: ${url}`);
          return url;
        }
      } else {
        console.log(`❌ 响应错误: ${url} - ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ 请求失败: ${url} - ${error.message}`);
    }
  }
  
  console.log('❌ 未找到有效的API服务');
  return null;
}

// 导出测试函数
window.debugApiDiscovery = testUrls;

console.log('调试脚本已加载，在控制台运行 debugApiDiscovery() 来测试');

export { testUrls };
