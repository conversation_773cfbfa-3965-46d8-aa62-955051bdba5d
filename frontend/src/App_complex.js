import React, { useState, useRef } from 'react';
import UploadForm from './UploadForm';
import ResultView from './ResultView';
import { getApiUrl } from './config/api';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const uploadFormRef = useRef(null);

  const handleUpload = async (formData) => {
    setIsLoading(true);
    setError(null);
    
    // 保存原始图像用于对比
    const file = formData.get('file');
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setOriginalImage(e.target.result);
      reader.readAsDataURL(file);
    }

    try {
      // 使用自动发现的API地址
      const apiUrl = await getApiUrl('/enhance/');
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setResult(null);
    setError(null);
    setOriginalImage(null);
  };

  const handleApply = () => {
    if (uploadFormRef.current) {
      uploadFormRef.current.triggerSubmit();
    }
  };

  return (
    <div style={{ 
      display: 'flex', 
      height: '100vh', 
      backgroundColor: '#1a1a1a',
      color: '#fff',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    }}>
      {/* 主工作区 */}
      <div style={{ 
        flex: 1, 
        display: 'flex', 
        flexDirection: 'column',
        minWidth: 0
      }}>
        {/* 结果展示区域 */}
        <div style={{ 
          flex: 1, 
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column'
        }}>
          {error && (
            <div style={{
              padding: '12px',
              backgroundColor: '#ff4444',
              color: '#fff',
              margin: '8px',
              borderRadius: '4px',
              fontSize: '14px'
            }}>
              {error}
            </div>
          )}
          
          <ResultView 
            result={result} 
            originalImage={originalImage}
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* 右侧参数面板 */}
      <div style={{ 
        width: '320px', 
        backgroundColor: '#2b2b2b',
        borderLeft: '1px solid #444',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* 顶部工具栏 */}
        <div style={{
          padding: '12px 16px',
          borderBottom: '1px solid #444',
          backgroundColor: '#333',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ 
            fontSize: '14px', 
            fontWeight: '500',
            color: '#fff'
          }}>
            图像增强参数
          </div>
          
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={handleReset}
              style={{
                padding: '6px 12px',
                backgroundColor: '#555',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: 'pointer'
              }}
            >
              重置
            </button>
            <button
              onClick={handleApply}
              disabled={isLoading}
              style={{
                padding: '6px 12px',
                backgroundColor: isLoading ? '#666' : '#4a90e2',
                color: '#fff',
                border: 'none',
                borderRadius: '4px',
                fontSize: '12px',
                cursor: isLoading ? 'not-allowed' : 'pointer',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {isLoading && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
                  animation: 'loading 1.5s infinite'
                }} />
              )}
              {isLoading ? '处理中...' : '应用'}
            </button>
          </div>
        </div>

        {/* 参数配置区域 */}
        <div style={{ 
          flex: 1, 
          overflow: 'auto',
          padding: '16px'
        }}>
          <UploadForm 
            ref={uploadFormRef}
            onUpload={handleUpload} 
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* CSS动画 */}
      <style>{`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
}

export default App;
