import React, { useState } from 'react';
import { getApiUrl } from './config/api';

function App() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [apiResult, setApiResult] = useState(null);

  const testApiDiscovery = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('开始API发现测试...');
      
      const apiUrl = await getApiUrl('/');
      console.log('发现的API地址:', apiUrl);
      
      // 测试API响应
      const response = await fetch(apiUrl);
      const data = await response.json();
      
      setApiResult({
        url: apiUrl,
        response: data
      });
      
      alert(`API发现成功!\n地址: ${apiUrl}\n响应: ${data.message}`);
    } catch (error) {
      console.error('API发现失败:', error);
      setError(error.message);
      alert(`API发现失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{ 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>图像增强应用 - API测试版本</h1>
      <p>测试API自动发现功能</p>
      
      <div style={{
        backgroundColor: '#f0f0f0',
        padding: '15px',
        borderRadius: '5px',
        margin: '20px 0'
      }}>
        <h3>状态信息:</h3>
        <ul>
          <li>加载状态: {isLoading ? '测试中...' : '空闲'}</li>
          <li>错误信息: {error || '无'}</li>
          <li>API地址: {apiResult?.url || '未发现'}</li>
          <li>API响应: {apiResult?.response?.message || '无'}</li>
        </ul>
      </div>
      
      <button 
        onClick={testApiDiscovery}
        disabled={isLoading}
        style={{
          padding: '10px 20px',
          backgroundColor: isLoading ? '#666' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          marginRight: '10px'
        }}
      >
        {isLoading ? '测试中...' : '测试API发现'}
      </button>
      
      {error && (
        <div style={{
          padding: '12px',
          backgroundColor: '#ff4444',
          color: '#fff',
          margin: '8px 0',
          borderRadius: '4px',
          fontSize: '14px'
        }}>
          错误: {error}
        </div>
      )}
      
      {apiResult && (
        <div style={{
          padding: '12px',
          backgroundColor: '#44ff44',
          color: '#000',
          margin: '8px 0',
          borderRadius: '4px',
          fontSize: '14px'
        }}>
          <strong>API发现成功!</strong><br/>
          地址: {apiResult.url}<br/>
          响应: {JSON.stringify(apiResult.response, null, 2)}
        </div>
      )}
    </div>
  );
}

export default App;
