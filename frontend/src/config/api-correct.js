/**
 * API配置管理模块 - 正确版本
 */

class ApiConfig {
  constructor() {
    this.baseUrl = null;
    this.isDiscovering = false;
    this.discoveryPromise = null;
  }

  async discoverBackendUrl() {
    if (this.baseUrl) {
      return this.baseUrl;
    }

    if (this.isDiscovering) {
      return this.discoveryPromise;
    }

    this.isDiscovering = true;
    this.discoveryPromise = this._performDiscovery();

    try {
      this.baseUrl = await this.discoveryPromise;
      return this.baseUrl;
    } finally {
      this.isDiscovering = false;
    }
  }

  async _performDiscovery() {
    const candidates = this._getCandidateUrls();
    
    console.log('🔍 开始自动发现后端服务...', candidates);

    // 顺序测试候选地址（避免并发问题）
    for (const url of candidates) {
      try {
        const isValid = await this._testBackendUrl(url);
        if (isValid) {
          console.log('✅ 发现后端服务:', url);
          return url;
        }
      } catch (error) {
        console.log(`❌ 测试失败 ${url}:`, error.message);
      }
    }
    
    throw new Error('无法发现后端服务，请确保后端服务正在运行');
  }

  _getCandidateUrls() {
    const candidates = [];
    
    // 1. 环境变量配置的地址（优先级最高）
    if (process.env.REACT_APP_API_URL) {
      candidates.push(process.env.REACT_APP_API_URL);
    }
    
    // 2. 常见的后端地址
    const commonUrls = [
      'http://localhost:8001',
      'http://127.0.0.1:8001',
      'http://localhost:8000',
      'http://127.0.0.1:8000',
      'http://localhost:5000',
      'http://127.0.0.1:5000',
      'http://localhost:3001',
      'http://127.0.0.1:3001',
      'http://localhost:8080',
      'http://127.0.0.1:8080'
    ];
    
    candidates.push(...commonUrls);
    
    // 去重
    return [...new Set(candidates)];
  }

  async _testBackendUrl(url) {
    try {
      console.log(`🔍 测试地址: ${url}`);
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);
      
      const response = await fetch(`${url}/`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        }
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`✅ 响应成功: ${url}`, data);
        
        // 验证响应是否包含预期的字段
        if (data && data.message && data.message.includes('图像增强')) {
          return true;
        } else {
          console.log(`❌ 响应格式不正确: ${url}`);
        }
      } else {
        console.log(`❌ 响应状态错误: ${url} - ${response.status}`);
      }
      
      return false;
    } catch (error) {
      console.log(`❌ 请求失败: ${url} - ${error.message}`);
      return false;
    }
  }

  async getApiUrl(endpoint = '') {
    const baseUrl = await this.discoverBackendUrl();
    return `${baseUrl}${endpoint}`;
  }

  reset() {
    this.baseUrl = null;
    this.isDiscovering = false;
    this.discoveryPromise = null;
  }

  setBackendUrl(url) {
    this.baseUrl = url;
  }
}

// 创建全局实例
const apiConfig = new ApiConfig();

export default apiConfig;

// 便捷方法
export const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);
export const resetApiConfig = () => apiConfig.reset();
export const setBackendUrl = (url) => apiConfig.setBackendUrl(url);
