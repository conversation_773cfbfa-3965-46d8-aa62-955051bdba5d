/**
 * API配置管理模块
 * 实现前端自动发现后端服务地址的机制
 */

class ApiConfig {
  constructor() {
    this.baseUrl = null;
    this.isDiscovering = false;
    this.discoveryPromise = null;
  }

  /**
   * 自动发现后端服务地址
   * @returns {Promise<string>} 后端服务的基础URL
   */
  async discoverBackendUrl() {
    if (this.baseUrl) {
      return this.baseUrl;
    }

    if (this.isDiscovering) {
      return this.discoveryPromise;
    }

    this.isDiscovering = true;
    this.discoveryPromise = this._performDiscovery();

    try {
      this.baseUrl = await this.discoveryPromise;
      return this.baseUrl;
    } finally {
      this.isDiscovering = false;
    }
  }

  /**
   * 执行服务发现逻辑
   * @private
   */
  async _performDiscovery() {
    // 候选的后端服务地址列表
    const candidates = this._getCandidateUrls();
    
    console.log('🔍 开始自动发现后端服务...', candidates);

    // 并行测试所有候选地址
    const testPromises = candidates.map(url => this._testBackendUrl(url));
    
    try {
      // 使用Promise.allSettled等待所有测试完成
      const results = await Promise.allSettled(testPromises);
      
      // 找到第一个成功的地址
      for (let i = 0; i < results.length; i++) {
        if (results[i].status === 'fulfilled' && results[i].value) {
          const discoveredUrl = candidates[i];
          console.log('✅ 发现后端服务:', discoveredUrl);
          return discoveredUrl;
        }
      }
      
      // 如果所有地址都失败，抛出错误
      throw new Error('无法发现后端服务，请确保后端服务正在运行');
      
    } catch (error) {
      console.error('❌ 后端服务发现失败:', error);
      throw error;
    }
  }

  /**
   * 获取候选的后端服务地址
   * @private
   */
  _getCandidateUrls() {
    const currentHost = window.location.hostname;
    const currentProtocol = window.location.protocol;
    
    // 常见的后端端口
    const commonPorts = [8001, 8000, 5000, 3001, 8080];
    
    const candidates = [];
    
    // 1. 环境变量配置的地址（优先级最高）
    if (process.env.REACT_APP_API_URL) {
      candidates.push(process.env.REACT_APP_API_URL);
    }
    
    // 2. 同主机的常见端口
    for (const port of commonPorts) {
      candidates.push(`${currentProtocol}//${currentHost}:${port}`);
    }
    
    // 3. localhost的常见端口（如果当前不是localhost）
    if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {
      for (const port of commonPorts) {
        candidates.push(`${currentProtocol}//localhost:${port}`);
        candidates.push(`${currentProtocol}//127.0.0.1:${port}`);
      }
    }
    
    // 去重
    return [...new Set(candidates)];
  }

  /**
   * 测试后端服务地址是否可用
   * @private
   */
  async _testBackendUrl(url) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时
      
      const response = await fetch(`${url}/`, {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Accept': 'application/json',
        }
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        const data = await response.json();
        // 验证响应是否包含预期的字段
        if (data && data.message && data.message.includes('图像增强')) {
          return true;
        }
      }
      
      return false;
    } catch (error) {
      // 网络错误、超时等
      return false;
    }
  }

  /**
   * 获取完整的API URL
   */
  async getApiUrl(endpoint = '') {
    const baseUrl = await this.discoverBackendUrl();
    return `${baseUrl}${endpoint}`;
  }

  /**
   * 重置配置，强制重新发现
   */
  reset() {
    this.baseUrl = null;
    this.isDiscovering = false;
    this.discoveryPromise = null;
  }

  /**
   * 手动设置后端地址（用于测试或特殊情况）
   */
  setBackendUrl(url) {
    this.baseUrl = url;
  }
}

// 创建全局实例
const apiConfig = new ApiConfig();

export default apiConfig;

// 便捷方法
export const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);
export const resetApiConfig = () => apiConfig.reset();
export const setBackendUrl = (url) => apiConfig.setBackendUrl(url);
