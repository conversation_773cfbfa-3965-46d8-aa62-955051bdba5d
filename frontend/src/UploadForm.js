import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { getApiUrl } from './config/api';

const UploadForm = forwardRef(({ onUpload, isLoading }, ref) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [error, setError] = useState(null);
  const [presets, setPresets] = useState({});
  const [selectedPreset, setSelectedPreset] = useState('default');
  const [params, setParams] = useState({
    scale: 4,
    sharpening: 0.0,
    denoising: 0,
    saturation: 1.0,
    contrast: 1.0,
    brightness: 0,
    beauty: 0.0
  });

  // 获取预设配置
  useEffect(() => {
    const loadPresets = async () => {
      try {
        const apiUrl = await getApiUrl('/presets/');
        const res = await fetch(apiUrl);
        const data = await res.json();

        if (data && data.presets) {
          setPresets(data.presets);
          if (data.presets.default) {
            setParams(data.presets.default.params);
          }
        } else {
          console.warn('预设配置格式不正确:', data);
          // 设置默认的预设配置
          setPresets({
            default: { params: params },
            portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },
            landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },
            vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },
            fast: { params: { ...params, scale: 2 } }
          });
        }
      } catch (err) {
        console.error('获取预设配置失败:', err);
        // 设置默认的预设配置
        setPresets({
          default: { params: params },
          portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },
          landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },
          vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },
          fast: { params: { ...params, scale: 2 } }
        });
      }
    };

    loadPresets();
  }, []);

  // 处理预设选择
  const handlePresetChange = (presetKey) => {
    setSelectedPreset(presetKey);
    if (presets && presets[presetKey]) {
      setParams(presets[presetKey].params);
    }
  };

  // 处理参数变化
  const handleParamChange = (key, value) => {
    setParams(prev => ({
      ...prev,
      [key]: value
    }));
    setSelectedPreset('custom');
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) {
        setError('文件大小不能超过10MB');
        return;
      }

      if (!file.type.startsWith('image/')) {
        setError('请选择图像文件');
        return;
      }

      setSelectedFile(file);
      setError(null);

      const reader = new FileReader();
      reader.onload = (e) => setPreviewUrl(e.target.result);
      reader.readAsDataURL(file);
    } else {
      setSelectedFile(null);
      setPreviewUrl(null);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!selectedFile) {
      setError('请选择要增强的图像');
      return;
    }

    const formData = new FormData();
    formData.append("file", selectedFile);
    formData.append("params", JSON.stringify(params));
    onUpload(formData);
  };

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    triggerSubmit: () => {
      if (!selectedFile) {
        setError('请选择要增强的图像');
        return;
      }

      const formData = new FormData();
      formData.append("file", selectedFile);
      formData.append("params", JSON.stringify(params));
      onUpload(formData);
    }
  }));

  const SliderControl = ({ label, value, min, max, step, onChange, unit = '' }) => (
    <div style={{ marginBottom: '16px' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '6px'
      }}>
        <label style={{ 
          color: '#ddd', 
          fontSize: '13px',
          fontWeight: '500'
        }}>
          {label}
        </label>
        <span style={{ 
          color: '#aaa', 
          fontSize: '12px',
          minWidth: '40px',
          textAlign: 'right'
        }}>
          {typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value}{unit}
        </span>
      </div>
      <input 
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={(e) => onChange(parseFloat(e.target.value))}
        style={{
          width: '100%',
          height: '4px',
          borderRadius: '2px',
          background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${((value - min) / (max - min)) * 100}%, #555 ${((value - min) / (max - min)) * 100}%, #555 100%)`,
          outline: 'none',
          appearance: 'none',
          cursor: 'pointer'
        }}
      />
    </div>
  );

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <form onSubmit={handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        
        {/* 文件上传区域 */}
        <div style={{ 
          padding: '16px',
          borderBottom: '1px solid #555'
        }}>
          <label style={{ 
            display: 'block', 
            marginBottom: '8px', 
            color: '#ddd',
            fontSize: '13px',
            fontWeight: '500'
          }}>
            选择图像文件
          </label>
          <input
            type="file"
            onChange={handleFileChange}
            accept="image/*"
            required
            disabled={isLoading}
            style={{
              width: '100%',
              padding: '8px',
              backgroundColor: '#333',
              border: '1px solid #555',
              borderRadius: '4px',
              color: '#ddd',
              fontSize: '12px'
            }}
          />
          
          {previewUrl && (
            <div style={{ marginTop: '12px', textAlign: 'center' }}>
              <img
                src={previewUrl}
                alt="预览"
                style={{
                  maxWidth: '100%',
                  maxHeight: '120px',
                  borderRadius: '4px',
                  border: '1px solid #555'
                }}
              />
            </div>
          )}
          
          {error && (
            <div style={{ 
              marginTop: '8px',
              color: '#ff6b6b', 
              fontSize: '12px'
            }}>
              {error}
            </div>
          )}
        </div>

        {/* 基本设置 */}
        <div style={{ 
          padding: '16px',
          borderBottom: '1px solid #555'
        }}>
          <h3 style={{ 
            margin: '0 0 12px 0', 
            color: '#fff',
            fontSize: '14px',
            fontWeight: '600'
          }}>
            基本设置
          </h3>
          
          {/* 预设选择 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ 
              display: 'block', 
              marginBottom: '6px', 
              color: '#ddd',
              fontSize: '13px',
              fontWeight: '500'
            }}>
              预设配置
            </label>
            <select 
              value={selectedPreset}
              onChange={(e) => handlePresetChange(e.target.value)}
              style={{ 
                width: '100%', 
                padding: '6px 8px', 
                backgroundColor: '#333',
                border: '1px solid #555',
                borderRadius: '4px',
                color: '#ddd',
                fontSize: '12px'
              }}
            >
              <option value="default">默认设置</option>
              <option value="portrait">人像优化</option>
              <option value="landscape">风景增强</option>
              <option value="vintage">复古风格</option>
              <option value="fast">快速处理</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          {/* 超分倍数 */}
          <SliderControl
            label="超分倍数"
            value={params.scale}
            min={2}
            max={4}
            step={2}
            onChange={(value) => handleParamChange('scale', value)}
            unit="x"
          />


        </div>

        {/* 高级调整 */}
        <div style={{
          flex: 1,
          overflowY: 'auto'
        }}>
          {/* 锐化和降噪 */}
          <div style={{
            padding: '16px',
            borderBottom: '1px solid #555'
          }}>
            <h3 style={{
              margin: '0 0 16px 0',
              color: '#fff',
              fontSize: '14px',
              fontWeight: '600'
            }}>
              锐化和降噪
            </h3>

            <SliderControl
              label="锐化"
              value={params.sharpening}
              min={0}
              max={1}
              step={0.05}
              onChange={(value) => handleParamChange('sharpening', value)}
            />

            <SliderControl
              label="降噪"
              value={params.denoising}
              min={0}
              max={30}
              step={1}
              onChange={(value) => handleParamChange('denoising', value)}
            />
          </div>

          {/* 色彩调整 */}
          <div style={{
            padding: '16px',
            borderBottom: '1px solid #555'
          }}>
            <h3 style={{
              margin: '0 0 16px 0',
              color: '#fff',
              fontSize: '14px',
              fontWeight: '600'
            }}>
              色彩调整
            </h3>

            <SliderControl
              label="饱和度"
              value={params.saturation}
              min={0}
              max={2}
              step={0.1}
              onChange={(value) => handleParamChange('saturation', value)}
            />

            <SliderControl
              label="对比度"
              value={params.contrast}
              min={0}
              max={2}
              step={0.05}
              onChange={(value) => handleParamChange('contrast', value)}
            />

            <SliderControl
              label="亮度"
              value={params.brightness}
              min={-100}
              max={100}
              step={5}
              onChange={(value) => handleParamChange('brightness', value)}
            />
          </div>

          {/* 美颜效果 */}
          <div style={{
            padding: '16px'
          }}>
            <h3 style={{
              margin: '0 0 16px 0',
              color: '#fff',
              fontSize: '14px',
              fontWeight: '600'
            }}>
              美颜效果
            </h3>

            <SliderControl
              label="美颜强度"
              value={params.beauty}
              min={0}
              max={1}
              step={0.05}
              onChange={(value) => handleParamChange('beauty', value)}
            />
          </div>
        </div>
      </form>
    </div>
  );
});

export default UploadForm;
