[{"/Users/<USER>/code/imageenhanceproweb/frontend/src/index.js": "1", "/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js": "2", "/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js": "3", "/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js": "4", "/Users/<USER>/code/imageenhanceproweb/frontend/src/config/api.js": "5"}, {"size": 232, "mtime": 1752406602411, "results": "6", "hashOfConfig": "7"}, {"size": 2850, "mtime": 1752491133629, "results": "8", "hashOfConfig": "7"}, {"size": 32514, "mtime": 1752490627106, "results": "9", "hashOfConfig": "7"}, {"size": 11313, "mtime": 1752490529001, "results": "10", "hashOfConfig": "7"}, {"size": 4423, "mtime": 1752490346664, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "wg8roi", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/code/imageenhanceproweb/frontend/src/index.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js", [], [], "/Users/<USER>/code/imageenhanceproweb/frontend/src/config/api.js", [], []]