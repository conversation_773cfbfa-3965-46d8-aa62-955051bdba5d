{"ast": null, "code": "import React,{useState,useEffect,forwardRef,useImperativeHandle}from'react';import{getApiUrl}from'./config/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UploadForm=/*#__PURE__*/forwardRef((_ref,ref)=>{let{onUpload,isLoading}=_ref;const[selectedFile,setSelectedFile]=useState(null);const[previewUrl,setPreviewUrl]=useState(null);const[error,setError]=useState(null);const[presets,setPresets]=useState({});const[selectedPreset,setSelectedPreset]=useState('default');const[params,setParams]=useState({scale:4,sharpening:0.0,denoising:0,saturation:1.0,contrast:1.0,brightness:0,beauty:0.0});// 获取预设配置\nuseEffect(()=>{const loadPresets=async()=>{try{const apiUrl=await getApiUrl('/presets/');const res=await fetch(apiUrl);const data=await res.json();if(data&&data.presets){setPresets(data.presets);if(data.presets.default){setParams(data.presets.default.params);}}else{console.warn('预设配置格式不正确:',data);// 设置默认的预设配置\nsetPresets({default:{params:params},portrait:{params:{...params,beauty:0.3,sharpening:0.2}},landscape:{params:{...params,saturation:1.2,contrast:1.1}},vintage:{params:{...params,saturation:0.8,contrast:0.9}},fast:{params:{...params,scale:2}}});}}catch(err){console.error('获取预设配置失败:',err);// 设置默认的预设配置\nsetPresets({default:{params:params},portrait:{params:{...params,beauty:0.3,sharpening:0.2}},landscape:{params:{...params,saturation:1.2,contrast:1.1}},vintage:{params:{...params,saturation:0.8,contrast:0.9}},fast:{params:{...params,scale:2}}});}};loadPresets();},[]);// 处理预设选择\nconst handlePresetChange=presetKey=>{setSelectedPreset(presetKey);if(presets&&presets[presetKey]){setParams(presets[presetKey].params);}};// 处理参数变化\nconst handleParamChange=(key,value)=>{setParams(prev=>({...prev,[key]:value}));setSelectedPreset('custom');};const handleFileChange=e=>{const file=e.target.files[0];if(file){if(file.size>10*1024*1024){setError('文件大小不能超过10MB');return;}if(!file.type.startsWith('image/')){setError('请选择图像文件');return;}setSelectedFile(file);setError(null);const reader=new FileReader();reader.onload=e=>setPreviewUrl(e.target.result);reader.readAsDataURL(file);}else{setSelectedFile(null);setPreviewUrl(null);}};const handleSubmit=e=>{e.preventDefault();if(!selectedFile){setError('请选择要增强的图像');return;}const formData=new FormData();formData.append(\"file\",selectedFile);formData.append(\"params\",JSON.stringify(params));onUpload(formData);};// 暴露给父组件的方法\nuseImperativeHandle(ref,()=>({triggerSubmit:()=>{if(!selectedFile){setError('请选择要增强的图像');return;}const formData=new FormData();formData.append(\"file\",selectedFile);formData.append(\"params\",JSON.stringify(params));onUpload(formData);}}));const SliderControl=_ref2=>{let{label,value,min,max,step,onChange,unit=''}=_ref2;return/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',justifyContent:'space-between',alignItems:'center',marginBottom:'6px'},children:[/*#__PURE__*/_jsx(\"label\",{style:{color:'#ddd',fontSize:'13px',fontWeight:'500'},children:label}),/*#__PURE__*/_jsxs(\"span\",{style:{color:'#aaa',fontSize:'12px',minWidth:'40px',textAlign:'right'},children:[typeof value==='number'?value.toFixed(step<1?1:0):value,unit]})]}),/*#__PURE__*/_jsx(\"input\",{type:\"range\",min:min,max:max,step:step,value:value,onChange:e=>onChange(parseFloat(e.target.value)),style:{width:'100%',height:'4px',borderRadius:'2px',background:`linear-gradient(to right, #4a90e2 0%, #4a90e2 ${(value-min)/(max-min)*100}%, #555 ${(value-min)/(max-min)*100}%, #555 100%)`,outline:'none',appearance:'none',cursor:'pointer'}})]});};return/*#__PURE__*/_jsx(\"div\",{style:{height:'100%',display:'flex',flexDirection:'column'},children:/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,style:{flex:1,display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'16px',borderBottom:'1px solid #555'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'8px',color:'#ddd',fontSize:'13px',fontWeight:'500'},children:\"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(\"input\",{type:\"file\",onChange:handleFileChange,accept:\"image/*\",required:true,disabled:isLoading,style:{width:'100%',padding:'8px',backgroundColor:'#333',border:'1px solid #555',borderRadius:'4px',color:'#ddd',fontSize:'12px'}}),previewUrl&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'12px',textAlign:'center'},children:/*#__PURE__*/_jsx(\"img\",{src:previewUrl,alt:\"\\u9884\\u89C8\",style:{maxWidth:'100%',maxHeight:'120px',borderRadius:'4px',border:'1px solid #555'}})}),error&&/*#__PURE__*/_jsx(\"div\",{style:{marginTop:'8px',color:'#ff6b6b',fontSize:'12px'},children:error})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'16px',borderBottom:'1px solid #555'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 12px 0',color:'#fff',fontSize:'14px',fontWeight:'600'},children:\"\\u57FA\\u672C\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsxs(\"div\",{style:{marginBottom:'16px'},children:[/*#__PURE__*/_jsx(\"label\",{style:{display:'block',marginBottom:'6px',color:'#ddd',fontSize:'13px',fontWeight:'500'},children:\"\\u9884\\u8BBE\\u914D\\u7F6E\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedPreset,onChange:e=>handlePresetChange(e.target.value),style:{width:'100%',padding:'6px 8px',backgroundColor:'#333',border:'1px solid #555',borderRadius:'4px',color:'#ddd',fontSize:'12px'},children:[/*#__PURE__*/_jsx(\"option\",{value:\"default\",children:\"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsx(\"option\",{value:\"portrait\",children:\"\\u4EBA\\u50CF\\u4F18\\u5316\"}),/*#__PURE__*/_jsx(\"option\",{value:\"landscape\",children:\"\\u98CE\\u666F\\u589E\\u5F3A\"}),/*#__PURE__*/_jsx(\"option\",{value:\"vintage\",children:\"\\u590D\\u53E4\\u98CE\\u683C\"}),/*#__PURE__*/_jsx(\"option\",{value:\"fast\",children:\"\\u5FEB\\u901F\\u5904\\u7406\"}),/*#__PURE__*/_jsx(\"option\",{value:\"custom\",children:\"\\u81EA\\u5B9A\\u4E49\"})]})]}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u8D85\\u5206\\u500D\\u6570\",value:params.scale,min:2,max:4,step:2,onChange:value=>handleParamChange('scale',value),unit:\"x\"})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,overflowY:'auto'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'16px',borderBottom:'1px solid #555'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 16px 0',color:'#fff',fontSize:'14px',fontWeight:'600'},children:\"\\u9510\\u5316\\u548C\\u964D\\u566A\"}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u9510\\u5316\",value:params.sharpening,min:0,max:1,step:0.05,onChange:value=>handleParamChange('sharpening',value)}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u964D\\u566A\",value:params.denoising,min:0,max:30,step:1,onChange:value=>handleParamChange('denoising',value)})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'16px',borderBottom:'1px solid #555'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 16px 0',color:'#fff',fontSize:'14px',fontWeight:'600'},children:\"\\u8272\\u5F69\\u8C03\\u6574\"}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u9971\\u548C\\u5EA6\",value:params.saturation,min:0,max:2,step:0.1,onChange:value=>handleParamChange('saturation',value)}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u5BF9\\u6BD4\\u5EA6\",value:params.contrast,min:0,max:2,step:0.05,onChange:value=>handleParamChange('contrast',value)}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u4EAE\\u5EA6\",value:params.brightness,min:-100,max:100,step:5,onChange:value=>handleParamChange('brightness',value)})]}),/*#__PURE__*/_jsxs(\"div\",{style:{padding:'16px'},children:[/*#__PURE__*/_jsx(\"h3\",{style:{margin:'0 0 16px 0',color:'#fff',fontSize:'14px',fontWeight:'600'},children:\"\\u7F8E\\u989C\\u6548\\u679C\"}),/*#__PURE__*/_jsx(SliderControl,{label:\"\\u7F8E\\u989C\\u5F3A\\u5EA6\",value:params.beauty,min:0,max:1,step:0.05,onChange:value=>handleParamChange('beauty',value)})]})]})]})});});export default UploadForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "getApiUrl", "jsx", "_jsx", "jsxs", "_jsxs", "UploadForm", "_ref", "ref", "onUpload", "isLoading", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "params", "setParams", "scale", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "loadPresets", "apiUrl", "res", "fetch", "data", "json", "default", "console", "warn", "portrait", "landscape", "vintage", "fast", "err", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "size", "type", "startsWith", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "triggerSubmit", "SliderControl", "_ref2", "label", "min", "max", "step", "onChange", "unit", "style", "marginBottom", "children", "display", "justifyContent", "alignItems", "color", "fontSize", "fontWeight", "min<PERSON><PERSON><PERSON>", "textAlign", "toFixed", "parseFloat", "width", "height", "borderRadius", "background", "outline", "appearance", "cursor", "flexDirection", "onSubmit", "flex", "padding", "borderBottom", "accept", "required", "disabled", "backgroundColor", "border", "marginTop", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "margin", "overflowY"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { getApiUrl } from './config/api';\n\nconst UploadForm = forwardRef(({ onUpload, isLoading }, ref) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    const loadPresets = async () => {\n      try {\n        const apiUrl = await getApiUrl('/presets/');\n        const res = await fetch(apiUrl);\n        const data = await res.json();\n\n        if (data && data.presets) {\n          setPresets(data.presets);\n          if (data.presets.default) {\n            setParams(data.presets.default.params);\n          }\n        } else {\n          console.warn('预设配置格式不正确:', data);\n          // 设置默认的预设配置\n          setPresets({\n            default: { params: params },\n            portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },\n            landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },\n            vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },\n            fast: { params: { ...params, scale: 2 } }\n          });\n        }\n      } catch (err) {\n        console.error('获取预设配置失败:', err);\n        // 设置默认的预设配置\n        setPresets({\n          default: { params: params },\n          portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },\n          landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },\n          vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },\n          fast: { params: { ...params, scale: 2 } }\n        });\n      }\n    };\n\n    loadPresets();\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      setSelectedFile(file);\n      setError(null);\n\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  // 暴露给父组件的方法\n  useImperativeHandle(ref, () => ({\n    triggerSubmit: () => {\n      if (!selectedFile) {\n        setError('请选择要增强的图像');\n        return;\n      }\n\n      const formData = new FormData();\n      formData.append(\"file\", selectedFile);\n      formData.append(\"params\", JSON.stringify(params));\n      onUpload(formData);\n    }\n  }));\n\n  const SliderControl = ({ label, value, min, max, step, onChange, unit = '' }) => (\n    <div style={{ marginBottom: '16px' }}>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        marginBottom: '6px'\n      }}>\n        <label style={{ \n          color: '#ddd', \n          fontSize: '13px',\n          fontWeight: '500'\n        }}>\n          {label}\n        </label>\n        <span style={{ \n          color: '#aaa', \n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        }}>\n          {typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value}{unit}\n        </span>\n      </div>\n      <input \n        type=\"range\"\n        min={min}\n        max={max}\n        step={step}\n        value={value}\n        onChange={(e) => onChange(parseFloat(e.target.value))}\n        style={{\n          width: '100%',\n          height: '4px',\n          borderRadius: '2px',\n          background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${((value - min) / (max - min)) * 100}%, #555 ${((value - min) / (max - min)) * 100}%, #555 100%)`,\n          outline: 'none',\n          appearance: 'none',\n          cursor: 'pointer'\n        }}\n      />\n    </div>\n  );\n\n  return (\n    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <form onSubmit={handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n        \n        {/* 文件上传区域 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <label style={{ \n            display: 'block', \n            marginBottom: '8px', \n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          }}>\n            选择图像文件\n          </label>\n          <input\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{\n              width: '100%',\n              padding: '8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            }}\n          />\n          \n          {previewUrl && (\n            <div style={{ marginTop: '12px', textAlign: 'center' }}>\n              <img\n                src={previewUrl}\n                alt=\"预览\"\n                style={{\n                  maxWidth: '100%',\n                  maxHeight: '120px',\n                  borderRadius: '4px',\n                  border: '1px solid #555'\n                }}\n              />\n            </div>\n          )}\n          \n          {error && (\n            <div style={{ \n              marginTop: '8px',\n              color: '#ff6b6b', \n              fontSize: '12px'\n            }}>\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 基本设置 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <h3 style={{ \n            margin: '0 0 12px 0', \n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          }}>\n            基本设置\n          </h3>\n          \n          {/* 预设选择 */}\n          <div style={{ marginBottom: '16px' }}>\n            <label style={{ \n              display: 'block', \n              marginBottom: '6px', \n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            }}>\n              预设配置\n            </label>\n            <select \n              value={selectedPreset}\n              onChange={(e) => handlePresetChange(e.target.value)}\n              style={{ \n                width: '100%', \n                padding: '6px 8px', \n                backgroundColor: '#333',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                color: '#ddd',\n                fontSize: '12px'\n              }}\n            >\n              <option value=\"default\">默认设置</option>\n              <option value=\"portrait\">人像优化</option>\n              <option value=\"landscape\">风景增强</option>\n              <option value=\"vintage\">复古风格</option>\n              <option value=\"fast\">快速处理</option>\n              <option value=\"custom\">自定义</option>\n            </select>\n          </div>\n\n          {/* 超分倍数 */}\n          <SliderControl\n            label=\"超分倍数\"\n            value={params.scale}\n            min={2}\n            max={4}\n            step={2}\n            onChange={(value) => handleParamChange('scale', value)}\n            unit=\"x\"\n          />\n\n\n        </div>\n\n        {/* 高级调整 */}\n        <div style={{\n          flex: 1,\n          overflowY: 'auto'\n        }}>\n          {/* 锐化和降噪 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              锐化和降噪\n            </h3>\n\n            <SliderControl\n              label=\"锐化\"\n              value={params.sharpening}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('sharpening', value)}\n            />\n\n            <SliderControl\n              label=\"降噪\"\n              value={params.denoising}\n              min={0}\n              max={30}\n              step={1}\n              onChange={(value) => handleParamChange('denoising', value)}\n            />\n          </div>\n\n          {/* 色彩调整 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              色彩调整\n            </h3>\n\n            <SliderControl\n              label=\"饱和度\"\n              value={params.saturation}\n              min={0}\n              max={2}\n              step={0.1}\n              onChange={(value) => handleParamChange('saturation', value)}\n            />\n\n            <SliderControl\n              label=\"对比度\"\n              value={params.contrast}\n              min={0}\n              max={2}\n              step={0.05}\n              onChange={(value) => handleParamChange('contrast', value)}\n            />\n\n            <SliderControl\n              label=\"亮度\"\n              value={params.brightness}\n              min={-100}\n              max={100}\n              step={5}\n              onChange={(value) => handleParamChange('brightness', value)}\n            />\n          </div>\n\n          {/* 美颜效果 */}\n          <div style={{\n            padding: '16px'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              美颜效果\n            </h3>\n\n            <SliderControl\n              label=\"美颜强度\"\n              value={params.beauty}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('beauty', value)}\n            />\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n});\n\nexport default UploadForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,CAAEC,mBAAmB,KAAQ,OAAO,CACnF,OAASC,SAAS,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,UAAU,cAAGP,UAAU,CAAC,CAAAQ,IAAA,CAA0BC,GAAG,GAAK,IAAjC,CAAEC,QAAQ,CAAEC,SAAU,CAAC,CAAAH,IAAA,CACpD,KAAM,CAACI,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC1C,KAAM,CAACsB,cAAc,CAAEC,iBAAiB,CAAC,CAAGvB,QAAQ,CAAC,SAAS,CAAC,CAC/D,KAAM,CAACwB,MAAM,CAAEC,SAAS,CAAC,CAAGzB,QAAQ,CAAC,CACnC0B,KAAK,CAAE,CAAC,CACRC,UAAU,CAAE,GAAG,CACfC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,GAAG,CACbC,UAAU,CAAE,CAAC,CACbC,MAAM,CAAE,GACV,CAAC,CAAC,CAEF;AACA/B,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgC,WAAW,CAAG,KAAAA,CAAA,GAAY,CAC9B,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA9B,SAAS,CAAC,WAAW,CAAC,CAC3C,KAAM,CAAA+B,GAAG,CAAG,KAAM,CAAAC,KAAK,CAACF,MAAM,CAAC,CAC/B,KAAM,CAAAG,IAAI,CAAG,KAAM,CAAAF,GAAG,CAACG,IAAI,CAAC,CAAC,CAE7B,GAAID,IAAI,EAAIA,IAAI,CAACjB,OAAO,CAAE,CACxBC,UAAU,CAACgB,IAAI,CAACjB,OAAO,CAAC,CACxB,GAAIiB,IAAI,CAACjB,OAAO,CAACmB,OAAO,CAAE,CACxBd,SAAS,CAACY,IAAI,CAACjB,OAAO,CAACmB,OAAO,CAACf,MAAM,CAAC,CACxC,CACF,CAAC,IAAM,CACLgB,OAAO,CAACC,IAAI,CAAC,YAAY,CAAEJ,IAAI,CAAC,CAChC;AACAhB,UAAU,CAAC,CACTkB,OAAO,CAAE,CAAEf,MAAM,CAAEA,MAAO,CAAC,CAC3BkB,QAAQ,CAAE,CAAElB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEQ,MAAM,CAAE,GAAG,CAAEL,UAAU,CAAE,GAAI,CAAE,CAAC,CACjEgB,SAAS,CAAE,CAAEnB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEK,UAAU,CAAE,GAAG,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAC,CACpEc,OAAO,CAAE,CAAEpB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEK,UAAU,CAAE,GAAG,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAC,CAClEe,IAAI,CAAE,CAAErB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEE,KAAK,CAAE,CAAE,CAAE,CAC1C,CAAC,CAAC,CACJ,CACF,CAAE,MAAOoB,GAAG,CAAE,CACZN,OAAO,CAACtB,KAAK,CAAC,WAAW,CAAE4B,GAAG,CAAC,CAC/B;AACAzB,UAAU,CAAC,CACTkB,OAAO,CAAE,CAAEf,MAAM,CAAEA,MAAO,CAAC,CAC3BkB,QAAQ,CAAE,CAAElB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEQ,MAAM,CAAE,GAAG,CAAEL,UAAU,CAAE,GAAI,CAAE,CAAC,CACjEgB,SAAS,CAAE,CAAEnB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEK,UAAU,CAAE,GAAG,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAC,CACpEc,OAAO,CAAE,CAAEpB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEK,UAAU,CAAE,GAAG,CAAEC,QAAQ,CAAE,GAAI,CAAE,CAAC,CAClEe,IAAI,CAAE,CAAErB,MAAM,CAAE,CAAE,GAAGA,MAAM,CAAEE,KAAK,CAAE,CAAE,CAAE,CAC1C,CAAC,CAAC,CACJ,CACF,CAAC,CAEDO,WAAW,CAAC,CAAC,CACf,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAc,kBAAkB,CAAIC,SAAS,EAAK,CACxCzB,iBAAiB,CAACyB,SAAS,CAAC,CAC5B,GAAI5B,OAAO,EAAIA,OAAO,CAAC4B,SAAS,CAAC,CAAE,CACjCvB,SAAS,CAACL,OAAO,CAAC4B,SAAS,CAAC,CAACxB,MAAM,CAAC,CACtC,CACF,CAAC,CAED;AACA,KAAM,CAAAyB,iBAAiB,CAAGA,CAACC,GAAG,CAAEC,KAAK,GAAK,CACxC1B,SAAS,CAAC2B,IAAI,GAAK,CACjB,GAAGA,IAAI,CACP,CAACF,GAAG,EAAGC,KACT,CAAC,CAAC,CAAC,CACH5B,iBAAiB,CAAC,QAAQ,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA8B,gBAAgB,CAAIC,CAAC,EAAK,CAC9B,KAAM,CAAAC,IAAI,CAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAIF,IAAI,CAAE,CACR,GAAIA,IAAI,CAACG,IAAI,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAAE,CAChCvC,QAAQ,CAAC,cAAc,CAAC,CACxB,OACF,CAEA,GAAI,CAACoC,IAAI,CAACI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,CAAE,CACnCzC,QAAQ,CAAC,SAAS,CAAC,CACnB,OACF,CAEAJ,eAAe,CAACwC,IAAI,CAAC,CACrBpC,QAAQ,CAAC,IAAI,CAAC,CAEd,KAAM,CAAA0C,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIT,CAAC,EAAKrC,aAAa,CAACqC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC,CACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC,CAC5B,CAAC,IAAM,CACLxC,eAAe,CAAC,IAAI,CAAC,CACrBE,aAAa,CAAC,IAAI,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAAiD,YAAY,CAAIZ,CAAC,EAAK,CAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC,CAElB,GAAI,CAACrD,YAAY,CAAE,CACjBK,QAAQ,CAAC,WAAW,CAAC,CACrB,OACF,CAEA,KAAM,CAAAiD,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAExD,YAAY,CAAC,CACrCsD,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAEC,IAAI,CAACC,SAAS,CAAChD,MAAM,CAAC,CAAC,CACjDZ,QAAQ,CAACwD,QAAQ,CAAC,CACpB,CAAC,CAED;AACAjE,mBAAmB,CAACQ,GAAG,CAAE,KAAO,CAC9B8D,aAAa,CAAEA,CAAA,GAAM,CACnB,GAAI,CAAC3D,YAAY,CAAE,CACjBK,QAAQ,CAAC,WAAW,CAAC,CACrB,OACF,CAEA,KAAM,CAAAiD,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAExD,YAAY,CAAC,CACrCsD,QAAQ,CAACE,MAAM,CAAC,QAAQ,CAAEC,IAAI,CAACC,SAAS,CAAChD,MAAM,CAAC,CAAC,CACjDZ,QAAQ,CAACwD,QAAQ,CAAC,CACpB,CACF,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAM,aAAa,CAAGC,KAAA,MAAC,CAAEC,KAAK,CAAEzB,KAAK,CAAE0B,GAAG,CAAEC,GAAG,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,IAAI,CAAG,EAAG,CAAC,CAAAN,KAAA,oBAC1EnE,KAAA,QAAK0E,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnC5E,KAAA,QAAK0E,KAAK,CAAE,CACVG,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBJ,YAAY,CAAE,KAChB,CAAE,CAAAC,QAAA,eACA9E,IAAA,UAAO4E,KAAK,CAAE,CACZM,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CACCR,KAAK,CACD,CAAC,cACRpE,KAAA,SAAM0E,KAAK,CAAE,CACXM,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBE,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,OACb,CAAE,CAAAR,QAAA,EACC,MAAO,CAAAjC,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAAC0C,OAAO,CAACd,IAAI,CAAG,CAAC,CAAG,CAAC,CAAG,CAAC,CAAC,CAAG5B,KAAK,CAAE8B,IAAI,EACtE,CAAC,EACJ,CAAC,cACN3E,IAAA,UACEqD,IAAI,CAAC,OAAO,CACZkB,GAAG,CAAEA,GAAI,CACTC,GAAG,CAAEA,GAAI,CACTC,IAAI,CAAEA,IAAK,CACX5B,KAAK,CAAEA,KAAM,CACb6B,QAAQ,CAAG1B,CAAC,EAAK0B,QAAQ,CAACc,UAAU,CAACxC,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE,CACtD+B,KAAK,CAAE,CACLa,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,iDAAkD,CAAC/C,KAAK,CAAG0B,GAAG,GAAKC,GAAG,CAAGD,GAAG,CAAC,CAAI,GAAG,WAAY,CAAC1B,KAAK,CAAG0B,GAAG,GAAKC,GAAG,CAAGD,GAAG,CAAC,CAAI,GAAG,eAAe,CAC7JsB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,MAAM,CAClBC,MAAM,CAAE,SACV,CAAE,CACH,CAAC,EACC,CAAC,EACP,CAED,mBACE/F,IAAA,QAAK4E,KAAK,CAAE,CAAEc,MAAM,CAAE,MAAM,CAAEX,OAAO,CAAE,MAAM,CAAEiB,aAAa,CAAE,QAAS,CAAE,CAAAlB,QAAA,cACvE5E,KAAA,SAAM+F,QAAQ,CAAErC,YAAa,CAACgB,KAAK,CAAE,CAAEsB,IAAI,CAAE,CAAC,CAAEnB,OAAO,CAAE,MAAM,CAAEiB,aAAa,CAAE,QAAS,CAAE,CAAAlB,QAAA,eAGzF5E,KAAA,QAAK0E,KAAK,CAAE,CACVuB,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,gBAChB,CAAE,CAAAtB,QAAA,eACA9E,IAAA,UAAO4E,KAAK,CAAE,CACZG,OAAO,CAAE,OAAO,CAChBF,YAAY,CAAE,KAAK,CACnBK,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CAAC,sCAEH,CAAO,CAAC,cACR9E,IAAA,UACEqD,IAAI,CAAC,MAAM,CACXqB,QAAQ,CAAE3B,gBAAiB,CAC3BsD,MAAM,CAAC,SAAS,CAChBC,QAAQ,MACRC,QAAQ,CAAEhG,SAAU,CACpBqE,KAAK,CAAE,CACLa,KAAK,CAAE,MAAM,CACbU,OAAO,CAAE,KAAK,CACdK,eAAe,CAAE,MAAM,CACvBC,MAAM,CAAE,gBAAgB,CACxBd,YAAY,CAAE,KAAK,CACnBT,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MACZ,CAAE,CACH,CAAC,CAEDzE,UAAU,eACTV,IAAA,QAAK4E,KAAK,CAAE,CAAE8B,SAAS,CAAE,MAAM,CAAEpB,SAAS,CAAE,QAAS,CAAE,CAAAR,QAAA,cACrD9E,IAAA,QACE2G,GAAG,CAAEjG,UAAW,CAChBkG,GAAG,CAAC,cAAI,CACRhC,KAAK,CAAE,CACLiC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,OAAO,CAClBnB,YAAY,CAAE,KAAK,CACnBc,MAAM,CAAE,gBACV,CAAE,CACH,CAAC,CACC,CACN,CAEA7F,KAAK,eACJZ,IAAA,QAAK4E,KAAK,CAAE,CACV8B,SAAS,CAAE,KAAK,CAChBxB,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,MACZ,CAAE,CAAAL,QAAA,CACClE,KAAK,CACH,CACN,EACE,CAAC,cAGNV,KAAA,QAAK0E,KAAK,CAAE,CACVuB,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,gBAChB,CAAE,CAAAtB,QAAA,eACA9E,IAAA,OAAI4E,KAAK,CAAE,CACTmC,MAAM,CAAE,YAAY,CACpB7B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CAAC,0BAEH,CAAI,CAAC,cAGL5E,KAAA,QAAK0E,KAAK,CAAE,CAAEC,YAAY,CAAE,MAAO,CAAE,CAAAC,QAAA,eACnC9E,IAAA,UAAO4E,KAAK,CAAE,CACZG,OAAO,CAAE,OAAO,CAChBF,YAAY,CAAE,KAAK,CACnBK,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CAAC,0BAEH,CAAO,CAAC,cACR5E,KAAA,WACE2C,KAAK,CAAE7B,cAAe,CACtB0D,QAAQ,CAAG1B,CAAC,EAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE,CACpD+B,KAAK,CAAE,CACLa,KAAK,CAAE,MAAM,CACbU,OAAO,CAAE,SAAS,CAClBK,eAAe,CAAE,MAAM,CACvBC,MAAM,CAAE,gBAAgB,CACxBd,YAAY,CAAE,KAAK,CACnBT,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MACZ,CAAE,CAAAL,QAAA,eAEF9E,IAAA,WAAQ6C,KAAK,CAAC,SAAS,CAAAiC,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACrC9E,IAAA,WAAQ6C,KAAK,CAAC,UAAU,CAAAiC,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACtC9E,IAAA,WAAQ6C,KAAK,CAAC,WAAW,CAAAiC,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACvC9E,IAAA,WAAQ6C,KAAK,CAAC,SAAS,CAAAiC,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACrC9E,IAAA,WAAQ6C,KAAK,CAAC,MAAM,CAAAiC,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAClC9E,IAAA,WAAQ6C,KAAK,CAAC,QAAQ,CAAAiC,QAAA,CAAC,oBAAG,CAAQ,CAAC,EAC7B,CAAC,EACN,CAAC,cAGN9E,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,0BAAM,CACZzB,KAAK,CAAE3B,MAAM,CAACE,KAAM,CACpBmD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,CAAE,CACPC,IAAI,CAAE,CAAE,CACRC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,OAAO,CAAEE,KAAK,CAAE,CACvD8B,IAAI,CAAC,GAAG,CACT,CAAC,EAGC,CAAC,cAGNzE,KAAA,QAAK0E,KAAK,CAAE,CACVsB,IAAI,CAAE,CAAC,CACPc,SAAS,CAAE,MACb,CAAE,CAAAlC,QAAA,eAEA5E,KAAA,QAAK0E,KAAK,CAAE,CACVuB,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,gBAChB,CAAE,CAAAtB,QAAA,eACA9E,IAAA,OAAI4E,KAAK,CAAE,CACTmC,MAAM,CAAE,YAAY,CACpB7B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CAAC,gCAEH,CAAI,CAAC,cAEL9E,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,cAAI,CACVzB,KAAK,CAAE3B,MAAM,CAACG,UAAW,CACzBkD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,CAAE,CACPC,IAAI,CAAE,IAAK,CACXC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,YAAY,CAAEE,KAAK,CAAE,CAC7D,CAAC,cAEF7C,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,cAAI,CACVzB,KAAK,CAAE3B,MAAM,CAACI,SAAU,CACxBiD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRC,IAAI,CAAE,CAAE,CACRC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,WAAW,CAAEE,KAAK,CAAE,CAC5D,CAAC,EACC,CAAC,cAGN3C,KAAA,QAAK0E,KAAK,CAAE,CACVuB,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,gBAChB,CAAE,CAAAtB,QAAA,eACA9E,IAAA,OAAI4E,KAAK,CAAE,CACTmC,MAAM,CAAE,YAAY,CACpB7B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CAAC,0BAEH,CAAI,CAAC,cAEL9E,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,oBAAK,CACXzB,KAAK,CAAE3B,MAAM,CAACK,UAAW,CACzBgD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,CAAE,CACPC,IAAI,CAAE,GAAI,CACVC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,YAAY,CAAEE,KAAK,CAAE,CAC7D,CAAC,cAEF7C,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,oBAAK,CACXzB,KAAK,CAAE3B,MAAM,CAACM,QAAS,CACvB+C,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,CAAE,CACPC,IAAI,CAAE,IAAK,CACXC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,UAAU,CAAEE,KAAK,CAAE,CAC3D,CAAC,cAEF7C,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,cAAI,CACVzB,KAAK,CAAE3B,MAAM,CAACO,UAAW,CACzB8C,GAAG,CAAE,CAAC,GAAI,CACVC,GAAG,CAAE,GAAI,CACTC,IAAI,CAAE,CAAE,CACRC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,YAAY,CAAEE,KAAK,CAAE,CAC7D,CAAC,EACC,CAAC,cAGN3C,KAAA,QAAK0E,KAAK,CAAE,CACVuB,OAAO,CAAE,MACX,CAAE,CAAArB,QAAA,eACA9E,IAAA,OAAI4E,KAAK,CAAE,CACTmC,MAAM,CAAE,YAAY,CACpB7B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAAN,QAAA,CAAC,0BAEH,CAAI,CAAC,cAEL9E,IAAA,CAACoE,aAAa,EACZE,KAAK,CAAC,0BAAM,CACZzB,KAAK,CAAE3B,MAAM,CAACQ,MAAO,CACrB6C,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,CAAE,CACPC,IAAI,CAAE,IAAK,CACXC,QAAQ,CAAG7B,KAAK,EAAKF,iBAAiB,CAAC,QAAQ,CAAEE,KAAK,CAAE,CACzD,CAAC,EACC,CAAC,EACH,CAAC,EACF,CAAC,CACJ,CAAC,CAEV,CAAC,CAAC,CAEF,cAAe,CAAA1C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}