{"ast": null, "code": "/**\n * API配置管理模块 - 修复版本\n * 实现前端自动发现后端服务地址的机制\n */\n\nclass ApiConfig {\n  constructor() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  /**\n   * 自动发现后端服务地址\n   * @returns {Promise<string>} 后端服务的基础URL\n   */\n  async discoverBackendUrl() {\n    if (this.baseUrl) {\n      return this.baseUrl;\n    }\n    if (this.isDiscovering) {\n      return this.discoveryPromise;\n    }\n    this.isDiscovering = true;\n    this.discoveryPromise = this._performDiscovery();\n    try {\n      this.baseUrl = await this.discoveryPromise;\n      return this.baseUrl;\n    } finally {\n      this.isDiscovering = false;\n    }\n  }\n\n  /**\n   * 执行服务发现逻辑\n   * @private\n   */\n  async _performDiscovery() {\n    // 候选的后端服务地址列表\n    const candidates = this._getCandidateUrls();\n    console.log('🔍 开始自动发现后端服务...', candidates);\n\n    // 并行测试所有候选地址\n    const testPromises = candidates.map(url => this._testBackendUrl(url));\n    try {\n      // 使用Promise.allSettled等待所有测试完成\n      const results = await Promise.allSettled(testPromises);\n\n      // 找到第一个成功的地址\n      for (let i = 0; i < results.length; i++) {\n        if (results[i].status === 'fulfilled' && results[i].value) {\n          const discoveredUrl = candidates[i];\n          console.log('✅ 发现后端服务:', discoveredUrl);\n          return discoveredUrl;\n        }\n      }\n\n      // 如果所有地址都失败，抛出错误\n      throw new Error('无法发现后端服务，请确保后端服务正在运行');\n    } catch (error) {\n      console.error('❌ 后端服务发现失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取候选的后端服务地址\n   * @private\n   */\n  _getCandidateUrls() {\n    const currentHost = window.location.hostname;\n    const currentProtocol = window.location.protocol;\n\n    // 常见的后端端口\n    const commonPorts = [8001, 8000, 5000, 3001, 8080];\n    const candidates = [];\n\n    // 1. 环境变量配置的地址（优先级最高）\n    if (process.env.REACT_APP_API_URL) {\n      candidates.push(process.env.REACT_APP_API_URL);\n    }\n\n    // 2. 同主机的常见端口\n    for (const port of commonPorts) {\n      candidates.push(`${currentProtocol}//${currentHost}:${port}`);\n    }\n\n    // 3. localhost的常见端口（如果当前不是localhost）\n    if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {\n      for (const port of commonPorts) {\n        candidates.push(`${currentProtocol}//localhost:${port}`);\n        candidates.push(`${currentProtocol}//127.0.0.1:${port}`);\n      }\n    }\n\n    // 去重\n    return [...new Set(candidates)];\n  }\n\n  /**\n   * 测试后端服务地址是否可用\n   * @private\n   */\n  async _testBackendUrl(url) {\n    try {\n      console.log(`🔍 测试地址: ${url}`);\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时\n\n      const response = await fetch(`${url}/`, {\n        method: 'GET',\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json'\n        }\n      });\n      clearTimeout(timeoutId);\n      if (response.ok) {\n        const data = await response.json();\n        console.log(`✅ 响应成功: ${url}`, data);\n        // 验证响应是否包含预期的字段\n        if (data && data.message && data.message.includes('图像增强')) {\n          return true;\n        } else {\n          console.log(`❌ 响应格式不正确: ${url}`);\n        }\n      } else {\n        console.log(`❌ 响应状态错误: ${url} - ${response.status}`);\n      }\n      return false;\n    } catch (error) {\n      console.log(`❌ 请求失败: ${url} - ${error.message}`);\n      return false;\n    }\n  }\n\n  /**\n   * 获取完整的API URL\n   */\n  async getApiUrl(endpoint = '') {\n    const baseUrl = await this.discoverBackendUrl();\n    return `${baseUrl}${endpoint}`;\n  }\n\n  /**\n   * 重置配置，强制重新发现\n   */\n  reset() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  /**\n   * 手动设置后端地址（用于测试或特殊情况）\n   */\n  setBackendUrl(url) {\n    this.baseUrl = url;\n  }\n}\n\n// 创建全局实例\nconst apiConfig = new ApiConfig();\nexport default apiConfig;\n\n// 便捷方法\nexport const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);\nexport const resetApiConfig = () => apiConfig.reset();\nexport const setBackendUrl = url => apiConfig.setBackendUrl(url);", "map": {"version": 3, "names": ["ApiConfig", "constructor", "baseUrl", "isDiscovering", "discoveryPromise", "discoverBackendUrl", "_performDiscovery", "candidates", "_getCandidateUrls", "console", "log", "testPromises", "map", "url", "_testBackendUrl", "results", "Promise", "allSettled", "i", "length", "status", "value", "discoveredUrl", "Error", "error", "currentHost", "window", "location", "hostname", "currentProtocol", "protocol", "commonPorts", "process", "env", "REACT_APP_API_URL", "push", "port", "Set", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "method", "signal", "headers", "clearTimeout", "ok", "data", "json", "message", "includes", "getApiUrl", "endpoint", "reset", "setBackendUrl", "apiConfig", "resetApiConfig"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/config/api.js"], "sourcesContent": ["/**\n * API配置管理模块 - 修复版本\n * 实现前端自动发现后端服务地址的机制\n */\n\nclass ApiConfig {\n  constructor() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  /**\n   * 自动发现后端服务地址\n   * @returns {Promise<string>} 后端服务的基础URL\n   */\n  async discoverBackendUrl() {\n    if (this.baseUrl) {\n      return this.baseUrl;\n    }\n\n    if (this.isDiscovering) {\n      return this.discoveryPromise;\n    }\n\n    this.isDiscovering = true;\n    this.discoveryPromise = this._performDiscovery();\n\n    try {\n      this.baseUrl = await this.discoveryPromise;\n      return this.baseUrl;\n    } finally {\n      this.isDiscovering = false;\n    }\n  }\n\n  /**\n   * 执行服务发现逻辑\n   * @private\n   */\n  async _performDiscovery() {\n    // 候选的后端服务地址列表\n    const candidates = this._getCandidateUrls();\n    \n    console.log('🔍 开始自动发现后端服务...', candidates);\n\n    // 并行测试所有候选地址\n    const testPromises = candidates.map(url => this._testBackendUrl(url));\n    \n    try {\n      // 使用Promise.allSettled等待所有测试完成\n      const results = await Promise.allSettled(testPromises);\n      \n      // 找到第一个成功的地址\n      for (let i = 0; i < results.length; i++) {\n        if (results[i].status === 'fulfilled' && results[i].value) {\n          const discoveredUrl = candidates[i];\n          console.log('✅ 发现后端服务:', discoveredUrl);\n          return discoveredUrl;\n        }\n      }\n      \n      // 如果所有地址都失败，抛出错误\n      throw new Error('无法发现后端服务，请确保后端服务正在运行');\n      \n    } catch (error) {\n      console.error('❌ 后端服务发现失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取候选的后端服务地址\n   * @private\n   */\n  _getCandidateUrls() {\n    const currentHost = window.location.hostname;\n    const currentProtocol = window.location.protocol;\n    \n    // 常见的后端端口\n    const commonPorts = [8001, 8000, 5000, 3001, 8080];\n    \n    const candidates = [];\n    \n    // 1. 环境变量配置的地址（优先级最高）\n    if (process.env.REACT_APP_API_URL) {\n      candidates.push(process.env.REACT_APP_API_URL);\n    }\n    \n    // 2. 同主机的常见端口\n    for (const port of commonPorts) {\n      candidates.push(`${currentProtocol}//${currentHost}:${port}`);\n    }\n    \n    // 3. localhost的常见端口（如果当前不是localhost）\n    if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {\n      for (const port of commonPorts) {\n        candidates.push(`${currentProtocol}//localhost:${port}`);\n        candidates.push(`${currentProtocol}//127.0.0.1:${port}`);\n      }\n    }\n    \n    // 去重\n    return [...new Set(candidates)];\n  }\n\n  /**\n   * 测试后端服务地址是否可用\n   * @private\n   */\n  async _testBackendUrl(url) {\n    try {\n      console.log(`🔍 测试地址: ${url}`);\n      \n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时\n      \n      const response = await fetch(`${url}/`, {\n        method: 'GET',\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json',\n        }\n      });\n      \n      clearTimeout(timeoutId);\n      \n      if (response.ok) {\n        const data = await response.json();\n        console.log(`✅ 响应成功: ${url}`, data);\n        // 验证响应是否包含预期的字段\n        if (data && data.message && data.message.includes('图像增强')) {\n          return true;\n        } else {\n          console.log(`❌ 响应格式不正确: ${url}`);\n        }\n      } else {\n        console.log(`❌ 响应状态错误: ${url} - ${response.status}`);\n      }\n      \n      return false;\n    } catch (error) {\n      console.log(`❌ 请求失败: ${url} - ${error.message}`);\n      return false;\n    }\n  }\n\n  /**\n   * 获取完整的API URL\n   */\n  async getApiUrl(endpoint = '') {\n    const baseUrl = await this.discoverBackendUrl();\n    return `${baseUrl}${endpoint}`;\n  }\n\n  /**\n   * 重置配置，强制重新发现\n   */\n  reset() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  /**\n   * 手动设置后端地址（用于测试或特殊情况）\n   */\n  setBackendUrl(url) {\n    this.baseUrl = url;\n  }\n}\n\n// 创建全局实例\nconst apiConfig = new ApiConfig();\n\nexport default apiConfig;\n\n// 便捷方法\nexport const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);\nexport const resetApiConfig = () => apiConfig.reset();\nexport const setBackendUrl = (url) => apiConfig.setBackendUrl(url);\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,MAAMA,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;;EAEA;AACF;AACA;AACA;EACE,MAAMC,kBAAkBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACH,OAAO,EAAE;MAChB,OAAO,IAAI,CAACA,OAAO;IACrB;IAEA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,OAAO,IAAI,CAACC,gBAAgB;IAC9B;IAEA,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAEhD,IAAI;MACF,IAAI,CAACJ,OAAO,GAAG,MAAM,IAAI,CAACE,gBAAgB;MAC1C,OAAO,IAAI,CAACF,OAAO;IACrB,CAAC,SAAS;MACR,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMG,iBAAiBA,CAAA,EAAG;IACxB;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAE3CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,UAAU,CAAC;;IAE3C;IACA,MAAMI,YAAY,GAAGJ,UAAU,CAACK,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,eAAe,CAACD,GAAG,CAAC,CAAC;IAErE,IAAI;MACF;MACA,MAAME,OAAO,GAAG,MAAMC,OAAO,CAACC,UAAU,CAACN,YAAY,CAAC;;MAEtD;MACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;QACvC,IAAIH,OAAO,CAACG,CAAC,CAAC,CAACE,MAAM,KAAK,WAAW,IAAIL,OAAO,CAACG,CAAC,CAAC,CAACG,KAAK,EAAE;UACzD,MAAMC,aAAa,GAAGf,UAAU,CAACW,CAAC,CAAC;UACnCT,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEY,aAAa,CAAC;UACvC,OAAOA,aAAa;QACtB;MACF;;MAEA;MACA,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;IAEzC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;AACA;EACEhB,iBAAiBA,CAAA,EAAG;IAClB,MAAMiB,WAAW,GAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ;IAC5C,MAAMC,eAAe,GAAGH,MAAM,CAACC,QAAQ,CAACG,QAAQ;;IAEhD;IACA,MAAMC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAElD,MAAMxB,UAAU,GAAG,EAAE;;IAErB;IACA,IAAIyB,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;MACjC3B,UAAU,CAAC4B,IAAI,CAACH,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;IAChD;;IAEA;IACA,KAAK,MAAME,IAAI,IAAIL,WAAW,EAAE;MAC9BxB,UAAU,CAAC4B,IAAI,CAAC,GAAGN,eAAe,KAAKJ,WAAW,IAAIW,IAAI,EAAE,CAAC;IAC/D;;IAEA;IACA,IAAIX,WAAW,KAAK,WAAW,IAAIA,WAAW,KAAK,WAAW,EAAE;MAC9D,KAAK,MAAMW,IAAI,IAAIL,WAAW,EAAE;QAC9BxB,UAAU,CAAC4B,IAAI,CAAC,GAAGN,eAAe,eAAeO,IAAI,EAAE,CAAC;QACxD7B,UAAU,CAAC4B,IAAI,CAAC,GAAGN,eAAe,eAAeO,IAAI,EAAE,CAAC;MAC1D;IACF;;IAEA;IACA,OAAO,CAAC,GAAG,IAAIC,GAAG,CAAC9B,UAAU,CAAC,CAAC;EACjC;;EAEA;AACF;AACA;AACA;EACE,MAAMO,eAAeA,CAACD,GAAG,EAAE;IACzB,IAAI;MACFJ,OAAO,CAACC,GAAG,CAAC,YAAYG,GAAG,EAAE,CAAC;MAE9B,MAAMyB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAE9D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG/B,GAAG,GAAG,EAAE;QACtCgC,MAAM,EAAE,KAAK;QACbC,MAAM,EAAER,UAAU,CAACQ,MAAM;QACzBC,OAAO,EAAE;UACP,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MAEFC,YAAY,CAACR,SAAS,CAAC;MAEvB,IAAIG,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClC1C,OAAO,CAACC,GAAG,CAAC,WAAWG,GAAG,EAAE,EAAEqC,IAAI,CAAC;QACnC;QACA,IAAIA,IAAI,IAAIA,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACE,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;UACzD,OAAO,IAAI;QACb,CAAC,MAAM;UACL5C,OAAO,CAACC,GAAG,CAAC,cAAcG,GAAG,EAAE,CAAC;QAClC;MACF,CAAC,MAAM;QACLJ,OAAO,CAACC,GAAG,CAAC,aAAaG,GAAG,MAAM8B,QAAQ,CAACvB,MAAM,EAAE,CAAC;MACtD;MAEA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdf,OAAO,CAACC,GAAG,CAAC,WAAWG,GAAG,MAAMW,KAAK,CAAC4B,OAAO,EAAE,CAAC;MAChD,OAAO,KAAK;IACd;EACF;;EAEA;AACF;AACA;EACE,MAAME,SAASA,CAACC,QAAQ,GAAG,EAAE,EAAE;IAC7B,MAAMrD,OAAO,GAAG,MAAM,IAAI,CAACG,kBAAkB,CAAC,CAAC;IAC/C,OAAO,GAAGH,OAAO,GAAGqD,QAAQ,EAAE;EAChC;;EAEA;AACF;AACA;EACEC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACtD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;;EAEA;AACF;AACA;EACEqD,aAAaA,CAAC5C,GAAG,EAAE;IACjB,IAAI,CAACX,OAAO,GAAGW,GAAG;EACpB;AACF;;AAEA;AACA,MAAM6C,SAAS,GAAG,IAAI1D,SAAS,CAAC,CAAC;AAEjC,eAAe0D,SAAS;;AAExB;AACA,OAAO,MAAMJ,SAAS,GAAGA,CAACC,QAAQ,GAAG,EAAE,KAAKG,SAAS,CAACJ,SAAS,CAACC,QAAQ,CAAC;AACzE,OAAO,MAAMI,cAAc,GAAGA,CAAA,KAAMD,SAAS,CAACF,KAAK,CAAC,CAAC;AACrD,OAAO,MAAMC,aAAa,GAAI5C,GAAG,IAAK6C,SAAS,CAACD,aAAa,CAAC5C,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}