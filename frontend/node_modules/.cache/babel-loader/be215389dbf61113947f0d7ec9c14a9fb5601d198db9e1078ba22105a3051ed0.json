{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { getApiUrl } from './config/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ResultView = ({\n  result,\n  originalImage\n}) => {\n  _s();\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [apiBaseUrl, setApiBaseUrl] = useState('');\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({\n    x: 0,\n    y: 0\n  }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({\n    x: 0,\n    y: 0\n  });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n\n  // 获取API基础URL\n  useEffect(() => {\n    const loadApiUrl = async () => {\n      try {\n        const baseUrl = await getApiUrl('');\n        setApiBaseUrl(baseUrl);\n      } catch (error) {\n        console.error('获取API地址失败:', error);\n      }\n    };\n    loadApiUrl();\n  }, []);\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `${apiBaseUrl}/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleSplitMouseDown = e => {\n    setIsDragging(true);\n    e.preventDefault();\n    e.stopPropagation(); // 阻止事件冒泡，避免触发图像平移\n  };\n  const handleSplitMouseMove = e => {\n    if (!isDragging || !imageContainerRef.current) return;\n    const rect = imageContainerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, x / rect.width * 100));\n    setSplitPosition(percentage);\n  };\n  const handleSplitMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 缩放处理\n  const handleWheel = e => {\n    if (!imageContainerRef.current) return;\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = e => {\n    if (zoomLevel <= 1 || isDragging) return; // 只有放大时才允许平移，且不在拖拽分割线时\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n\n  // 图像容器的鼠标按下处理（避免与分割线拖拽冲突）\n  const handleImageMouseDown = e => {\n    // 检查是否点击在分割线上（通过检查目标元素）\n    if (e.target.closest('[data-split-line]')) {\n      return; // 如果点击在分割线上，不处理平移\n    }\n    if (zoomLevel > 1 && !isDragging) {\n      handlePanStart(e);\n    }\n  };\n  const handlePanMove = e => {\n    if (!isPanning) return;\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({\n      x: 0,\n      y: 0\n    });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleSplitMouseMove);\n      document.addEventListener('mouseup', handleSplitMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleSplitMouseMove);\n        document.removeEventListener('mouseup', handleSplitMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '13px',\n            color: '#ddd'\n          },\n          children: \"\\u5904\\u7406\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), result.message && /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [\"\\u2022 \", result.message]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '6px',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('split'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5206\\u5272\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setViewMode('side-by-side'),\n          style: {\n            padding: '4px 8px',\n            backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), viewMode === 'split' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: resetSplit,\n          style: {\n            padding: '4px 8px',\n            backgroundColor: '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '12px',\n            transition: 'background-color 0.2s'\n          },\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), result.params_used && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: showParams ? 'auto' : '32px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        padding: '8px 16px',\n        flexShrink: 0,\n        transition: 'height 0.3s ease'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontSize: '12px',\n              color: '#aaa'\n            },\n            children: \"\\u5904\\u7406\\u53C2\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), !showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '12px',\n              fontSize: '12px',\n              color: '#ddd'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: [result.params_used.scale, \"x\\u8D85\\u5206\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u9510\\u5316\", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u7F8E\\u989C\", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowParams(!showParams),\n          style: {\n            padding: '2px 6px',\n            backgroundColor: 'transparent',\n            color: '#aaa',\n            border: '1px solid #555',\n            borderRadius: '3px',\n            cursor: 'pointer',\n            fontSize: '11px',\n            transition: 'all 0.2s'\n          },\n          onMouseEnter: e => {\n            e.target.style.backgroundColor = '#555';\n            e.target.style.color = '#fff';\n          },\n          onMouseLeave: e => {\n            e.target.style.backgroundColor = 'transparent';\n            e.target.style.color = '#aaa';\n          },\n          children: showParams ? '▲' : '▼'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 11\n      }, this), showParams && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '12px',\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n          gap: '8px',\n          fontSize: '12px',\n          color: '#ddd'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u8D85\\u5206\\u500D\\u6570:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 20\n          }, this), \" \", result.params_used.scale, \"x\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"AI\\u6A21\\u578B:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 20\n          }, this), \" \", result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9510\\u5316:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.sharpening * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u964D\\u566A:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 20\n          }, this), \" \", result.params_used.denoising]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u9971\\u548C\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 20\n          }, this), \" \", result.params_used.saturation.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u5BF9\\u6BD4\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 20\n          }, this), \" \", result.params_used.contrast.toFixed(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u4EAE\\u5EA6:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 20\n          }, this), \" \", result.params_used.brightness > 0 ? '+' : '', result.params_used.brightness]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#aaa'\n            },\n            children: \"\\u7F8E\\u989C:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 20\n          }, this), \" \", (result.params_used.beauty * 100).toFixed(0), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      },\n      children: viewMode === 'split' ?\n      /*#__PURE__*/\n      // 分割线对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5206\\u5272\\u4F4D\\u7F6E\\uFF1A\", splitPosition.toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              position: 'relative',\n              zIndex: 1000 // 确保按钮始终在最上层\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomOut,\n              disabled: zoomLevel <= 0.5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u7F29\\u5C0F\",\n              children: \"\\u2212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                minWidth: '40px',\n                textAlign: 'center',\n                fontSize: '11px',\n                color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n              },\n              children: [(zoomLevel * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomIn,\n              disabled: zoomLevel >= 5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                color: zoomLevel >= 5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u653E\\u5927\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetZoom,\n              disabled: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#444' : '#555',\n                color: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? 'not-allowed' : 'pointer',\n                fontSize: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n              children: \"\\u2302\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: containerRef,\n          style: {\n            position: 'relative',\n            width: '100%',\n            height: '100%',\n            cursor: isDragging ? 'ew-resize' : 'default',\n            userSelect: 'none',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            overflow: 'auto' // 添加滚动条支持\n          },\n          children: originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              width: '100%',\n              height: '100%',\n              maxHeight: 'calc(100vh - 200px)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: imageContainerRef,\n              style: {\n                position: 'relative',\n                display: 'inline-block',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                overflow: 'hidden',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: handleImageMouseDown,\n              children: [imageError ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '400px',\n                  height: '300px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                },\n                children: \"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"img\", {\n                ref: enhancedImageRef,\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 250px)',\n                  height: 'auto',\n                  display: 'block',\n                  opacity: imageLoaded ? 1 : 0.5,\n                  imageRendering: 'auto' // 增强图像使用高质量渲染\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: `${splitPosition}%`,\n                  height: '100%',\n                  overflow: 'hidden'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: originalImage,\n                  alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                  style: {\n                    // 确保原图与增强图片尺寸完全匹配\n                    width: `${100 * 100 / splitPosition}%`,\n                    height: '100%',\n                    objectFit: 'fill',\n                    // 强制填充，实现最近邻插值效果\n                    imageRendering: 'pixelated',\n                    // 最近邻插值，保持像素清晰\n                    position: 'absolute',\n                    left: 0,\n                    top: 0\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 23\n              }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n                \"data-split-line\": \"true\",\n                style: {\n                  position: 'absolute',\n                  top: 0,\n                  left: `${splitPosition}%`,\n                  width: '2px',\n                  height: '100%',\n                  backgroundColor: '#4a90e2',\n                  cursor: 'ew-resize',\n                  boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                  transform: 'translateX(-1px)',\n                  zIndex: 10\n                },\n                onMouseDown: handleSplitMouseDown,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '50%',\n                    left: '50%',\n                    transform: 'translate(-50%, -50%)',\n                    width: '16px',\n                    height: '32px',\n                    backgroundColor: '#4a90e2',\n                    borderRadius: '8px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '10px',\n                    fontWeight: 'bold',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                  },\n                  children: \"\\u27F7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 566,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 23\n              }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  top: '50%',\n                  left: '50%',\n                  transform: 'translate(-50%, -50%)',\n                  color: '#aaa',\n                  backgroundColor: 'rgba(43,43,43,0.9)',\n                  padding: '8px 12px',\n                  borderRadius: '4px',\n                  fontSize: '12px'\n                },\n                children: \"\\u52A0\\u8F7D\\u4E2D...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this) :\n      /*#__PURE__*/\n      // 并排对比模式\n      _jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          padding: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '32px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            marginBottom: '12px',\n            fontSize: '12px',\n            color: '#aaa'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u5E76\\u6392\\u5BF9\\u6BD4\\u6A21\\u5F0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              position: 'relative',\n              zIndex: 1000 // 确保按钮始终在最上层\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomOut,\n              disabled: zoomLevel <= 0.5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u7F29\\u5C0F\",\n              children: \"\\u2212\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 639,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                minWidth: '40px',\n                textAlign: 'center',\n                fontSize: '11px',\n                color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n              },\n              children: [(zoomLevel * 100).toFixed(0), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 662,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: zoomIn,\n              disabled: zoomLevel >= 5,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                color: zoomLevel >= 5 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                fontSize: '12px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u653E\\u5927\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 671,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: resetZoom,\n              disabled: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0,\n              style: {\n                width: '24px',\n                height: '24px',\n                backgroundColor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#444' : '#555',\n                color: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? '#666' : '#fff',\n                border: '1px solid #666',\n                borderRadius: '3px',\n                cursor: zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0 ? 'not-allowed' : 'pointer',\n                fontSize: '10px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                position: 'relative',\n                zIndex: 1001\n              },\n              title: \"\\u91CD\\u7F6E\\u7F29\\u653E\",\n              children: \"\\u2302\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 694,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '16px',\n            alignItems: 'center',\n            justifyContent: 'center',\n            overflow: 'auto' // 添加滚动条支持\n          },\n          children: [originalImage && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              textAlign: 'center',\n              maxWidth: '50%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: \"\\u539F\\u59CB\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 735,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'inline-block',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: handleImageMouseDown,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: originalImage,\n                alt: \"\\u539F\\u59CB\\u56FE\\u50CF\",\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 300px)',\n                  height: 'auto',\n                  border: '1px solid #4a90e2',\n                  borderRadius: '4px',\n                  objectFit: 'contain',\n                  imageRendering: 'pixelated' // 最近邻插值，保持像素清晰\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 730,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: '1',\n              textAlign: 'center',\n              maxWidth: '50%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '8px',\n                fontSize: '12px',\n                color: '#aaa'\n              },\n              children: \"\\u589E\\u5F3A\\u56FE\\u50CF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 776,\n              columnNumber: 15\n            }, this), !imageLoaded && !imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '200px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px dashed #666',\n                borderRadius: '4px',\n                color: '#aaa',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 785,\n              columnNumber: 17\n            }, this), imageError && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '100%',\n                height: '200px',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                border: '1px solid #ff6b6b',\n                borderRadius: '4px',\n                color: '#ff6b6b',\n                backgroundColor: '#2b2b2b'\n              },\n              children: \"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 801,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: imageLoaded ? 'inline-block' : 'none',\n                transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                transformOrigin: 'center center',\n                transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                cursor: zoomLevel > 1 ? isPanning ? 'grabbing' : 'grab' : 'default'\n              },\n              onWheel: handleWheel,\n              onMouseDown: handleImageMouseDown,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: `http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`,\n                alt: \"\\u589E\\u5F3A\\u56FE\\u50CF\",\n                onLoad: handleImageLoad,\n                onError: handleImageError,\n                style: {\n                  maxWidth: '100%',\n                  maxHeight: 'calc(100vh - 300px)',\n                  height: 'auto',\n                  border: '1px solid #28ca42',\n                  borderRadius: '4px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 816,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 771,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: '48px',\n        backgroundColor: '#333',\n        borderTop: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        gap: '12px',\n        padding: '0 16px',\n        flexShrink: 0\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: downloadImage,\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#28ca42',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#22a83a',\n        onMouseLeave: e => e.target.style.backgroundColor = '#28ca42',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCE5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 879,\n          columnNumber: 13\n        }, this), \"\\u4E0B\\u8F7D\\u56FE\\u50CF\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 861,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n        href: `http://localhost:8001/result/${result.enhanced_url}`,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        style: {\n          padding: '6px 12px',\n          backgroundColor: '#4a90e2',\n          color: 'white',\n          textDecoration: 'none',\n          borderRadius: '4px',\n          fontSize: '13px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          transition: 'background-color 0.2s'\n        },\n        onMouseEnter: e => e.target.style.backgroundColor = '#357abd',\n        onMouseLeave: e => e.target.style.backgroundColor = '#4a90e2',\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDD0D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 902,\n          columnNumber: 13\n        }, this), \"\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 883,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '12px',\n          color: '#aaa',\n          marginLeft: 'auto'\n        },\n        children: [\"\\u6587\\u4EF6: \", result.filename]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 906,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 850,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 174,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultView, \"Nxnq8MOs5hoIo1qK988XnXwAEbA=\");\n_c = ResultView;\nexport default ResultView;\nvar _c;\n$RefreshReg$(_c, \"ResultView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "getApiUrl", "jsxDEV", "_jsxDEV", "ResultView", "result", "originalImage", "_s", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "apiBaseUrl", "setApiBaseUrl", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "zoomLevel", "setZoomLevel", "panOffset", "setPanOffset", "x", "y", "isPanning", "setIsPanning", "lastPanPoint", "setLastPanPoint", "containerRef", "enhancedImageRef", "imageContainerRef", "loadApiUrl", "baseUrl", "error", "console", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleSplitMouseDown", "e", "preventDefault", "stopPropagation", "handleSplitMouseMove", "current", "rect", "getBoundingClientRect", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleSplitMouseUp", "handleWheel", "delta", "deltaY", "newZoom", "handlePanStart", "clientY", "handleImageMouseDown", "target", "closest", "handlePanMove", "deltaX", "prev", "handlePanEnd", "resetZoom", "zoomIn", "zoomOut", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "justifyContent", "padding", "flexShrink", "gap", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "message", "onClick", "border", "cursor", "transition", "params_used", "scale", "use_realesrgan", "sharpening", "toFixed", "beauty", "onMouseEnter", "onMouseLeave", "marginTop", "gridTemplateColumns", "denoising", "saturation", "contrast", "brightness", "flex", "overflow", "position", "marginBottom", "zIndex", "disabled", "title", "min<PERSON><PERSON><PERSON>", "textAlign", "ref", "userSelect", "maxHeight", "transform", "transform<PERSON><PERSON>in", "onWheel", "onMouseDown", "src", "Date", "now", "alt", "onLoad", "onError", "max<PERSON><PERSON><PERSON>", "opacity", "imageRendering", "top", "objectFit", "boxShadow", "fontWeight", "borderTop", "rel", "textDecoration", "marginLeft", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { getApiUrl } from './config/api';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [apiBaseUrl, setApiBaseUrl] = useState('');\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n\n  // 获取API基础URL\n  useEffect(() => {\n    const loadApiUrl = async () => {\n      try {\n        const baseUrl = await getApiUrl('');\n        setApiBaseUrl(baseUrl);\n      } catch (error) {\n        console.error('获取API地址失败:', error);\n      }\n    };\n    loadApiUrl();\n  }, []);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `${apiBaseUrl}/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleSplitMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n    e.stopPropagation(); // 阻止事件冒泡，避免触发图像平移\n  };\n\n  const handleSplitMouseMove = (e) => {\n    if (!isDragging || !imageContainerRef.current) return;\n\n    const rect = imageContainerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleSplitMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 缩放处理\n  const handleWheel = (e) => {\n    if (!imageContainerRef.current) return;\n\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = (e) => {\n    if (zoomLevel <= 1 || isDragging) return; // 只有放大时才允许平移，且不在拖拽分割线时\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n\n  // 图像容器的鼠标按下处理（避免与分割线拖拽冲突）\n  const handleImageMouseDown = (e) => {\n    // 检查是否点击在分割线上（通过检查目标元素）\n    if (e.target.closest('[data-split-line]')) {\n      return; // 如果点击在分割线上，不处理平移\n    }\n\n    if (zoomLevel > 1 && !isDragging) {\n      handlePanStart(e);\n    }\n  };\n\n  const handlePanMove = (e) => {\n    if (!isPanning) return;\n\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({ x: 0, y: 0 });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleSplitMouseMove);\n      document.addEventListener('mouseup', handleSplitMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleSplitMouseMove);\n        document.removeEventListener('mouseup', handleSplitMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      }}>\n        {/* 左侧状态指示 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }}></div>\n          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>\n          {result.message && (\n            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>\n          )}\n        </div>\n\n        {/* 右侧视图控制 */}\n        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                transition: 'background-color 0.2s'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 参数信息栏 */}\n      {result.params_used && (\n        <div style={{\n          height: showParams ? 'auto' : '32px',\n          backgroundColor: '#2b2b2b',\n          borderBottom: '1px solid #555',\n          padding: '8px 16px',\n          flexShrink: 0,\n          transition: 'height 0.3s ease'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>\n              {!showParams && (\n                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>\n                  <span>{result.params_used.scale}x超分</span>\n                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>\n                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>\n                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '2px 6px',\n                backgroundColor: 'transparent',\n                color: '#aaa',\n                border: '1px solid #555',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px',\n                transition: 'all 0.2s'\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.backgroundColor = '#555';\n                e.target.style.color = '#fff';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.backgroundColor = 'transparent';\n                e.target.style.color = '#aaa';\n              }}\n            >\n              {showParams ? '▲' : '▼'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ \n              marginTop: '12px', \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n              gap: '8px',\n              fontSize: '12px',\n              color: '#ddd'\n            }}>\n              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>\n              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>\n              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 主图像显示区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 分割线信息栏和缩放控制 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>\n              </div>\n\n              {/* 缩放控制按钮 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                position: 'relative',\n                zIndex: 1000 // 确保按钮始终在最上层\n              }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            <div\n              ref={containerRef}\n              style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                cursor: isDragging ? 'ew-resize' : 'default',\n                userSelect: 'none',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                overflow: 'auto' // 添加滚动条支持\n              }}\n            >\n              {originalImage && (\n                <div style={{ \n                  position: 'relative', \n                  width: '100%', \n                  height: '100%',\n                  maxHeight: 'calc(100vh - 200px)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  {/* 图像对比容器 - 支持缩放和平移 */}\n                  <div\n                    ref={imageContainerRef}\n                    style={{\n                      position: 'relative',\n                      display: 'inline-block',\n                      border: '1px solid #555',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                      transformOrigin: 'center center',\n                      transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                      cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                    }}\n                    onWheel={handleWheel}\n                    onMouseDown={handleImageMouseDown}\n                  >\n                    {/* 增强图像作为背景 */}\n                    {imageError ? (\n                      <div style={{\n                        width: '400px',\n                        height: '300px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: '#ff6b6b',\n                        backgroundColor: '#2b2b2b'\n                      }}>\n                        增强图像加载失败\n                      </div>\n                    ) : (\n                      <img\n                        ref={enhancedImageRef}\n                        src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                        alt=\"增强图像\"\n                        onLoad={handleImageLoad}\n                        onError={handleImageError}\n                        style={{\n                          maxWidth: '100%',\n                          maxHeight: 'calc(100vh - 250px)',\n                          height: 'auto',\n                          display: 'block',\n                          opacity: imageLoaded ? 1 : 0.5,\n                          imageRendering: 'auto' // 增强图像使用高质量渲染\n                        }}\n                      />\n                    )}\n\n                    {/* 原始图像覆盖层 - 像素级对齐 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          width: `${splitPosition}%`,\n                          height: '100%',\n                          overflow: 'hidden'\n                        }}\n                      >\n                        <img\n                          src={originalImage}\n                          alt=\"原始图像\"\n                          style={{\n                            // 确保原图与增强图片尺寸完全匹配\n                            width: `${100 * 100 / splitPosition}%`,\n                            height: '100%',\n                            objectFit: 'fill', // 强制填充，实现最近邻插值效果\n                            imageRendering: 'pixelated', // 最近邻插值，保持像素清晰\n                            position: 'absolute',\n                            left: 0,\n                            top: 0\n                          }}\n                        />\n                      </div>\n                    )}\n\n                    {/* 分割线 */}\n                    {imageLoaded && (\n                      <div\n                        data-split-line=\"true\"\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: `${splitPosition}%`,\n                          width: '2px',\n                          height: '100%',\n                          backgroundColor: '#4a90e2',\n                          cursor: 'ew-resize',\n                          boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                          transform: 'translateX(-1px)',\n                          zIndex: 10\n                        }}\n                        onMouseDown={handleSplitMouseDown}\n                      >\n                        {/* 分割线手柄 */}\n                        <div\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: '50%',\n                            transform: 'translate(-50%, -50%)',\n                            width: '16px',\n                            height: '32px',\n                            backgroundColor: '#4a90e2',\n                            borderRadius: '8px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontSize: '10px',\n                            fontWeight: 'bold',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                          }}\n                        >\n                          ⟷\n                        </div>\n                      </div>\n                    )}\n\n                    {/* 加载状态 */}\n                    {!imageLoaded && !imageError && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        color: '#aaa',\n                        backgroundColor: 'rgba(43,43,43,0.9)',\n                        padding: '8px 12px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      }}>\n                        加载中...\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 并排对比控制栏 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <span>并排对比模式</span>\n\n              {/* 缩放控制按钮 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                position: 'relative',\n                zIndex: 1000 // 确保按钮始终在最上层\n              }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            {/* 并排图像容器 */}\n            <div style={{\n              flex: 1,\n              display: 'flex',\n              gap: '16px',\n              alignItems: 'center',\n              justifyContent: 'center',\n              overflow: 'auto' // 添加滚动条支持\n            }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ \n                flex: '1', \n                textAlign: 'center',\n                maxWidth: '50%'\n              }}>\n                <div style={{ \n                  marginBottom: '8px', \n                  fontSize: '12px', \n                  color: '#aaa' \n                }}>\n                  原始图像\n                </div>\n                <div\n                  style={{\n                    display: 'inline-block',\n                    transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                    transformOrigin: 'center center',\n                    transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                    cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                  }}\n                  onWheel={handleWheel}\n                  onMouseDown={handleImageMouseDown}\n                >\n                  <img\n                    src={originalImage}\n                    alt=\"原始图像\"\n                    style={{\n                      maxWidth: '100%',\n                      maxHeight: 'calc(100vh - 300px)',\n                      height: 'auto',\n                      border: '1px solid #4a90e2',\n                      borderRadius: '4px',\n                      objectFit: 'contain',\n                      imageRendering: 'pixelated' // 最近邻插值，保持像素清晰\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ \n              flex: '1', \n              textAlign: 'center',\n              maxWidth: '50%'\n            }}>\n              <div style={{ \n                marginBottom: '8px', \n                fontSize: '12px', \n                color: '#aaa' \n              }}>\n                增强图像\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #666',\n                  borderRadius: '4px',\n                  color: '#aaa',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  正在加载图像...\n                </div>\n              )}\n\n              {imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px solid #ff6b6b',\n                  borderRadius: '4px',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  图像加载失败\n                </div>\n              )}\n\n              <div\n                style={{\n                  display: imageLoaded ? 'inline-block' : 'none',\n                  transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                  transformOrigin: 'center center',\n                  transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                  cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                }}\n                onWheel={handleWheel}\n                onMouseDown={handleImageMouseDown}\n              >\n                <img\n                  src={`http://localhost:8001/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: 'calc(100vh - 300px)',\n                    height: 'auto',\n                    border: '1px solid #28ca42',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      {imageLoaded && (\n        <div style={{\n          height: '48px',\n          backgroundColor: '#333',\n          borderTop: '1px solid #555',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '12px',\n          padding: '0 16px',\n          flexShrink: 0\n        }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#28ca42',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}\n          >\n            <span>📥</span>\n            下载图像\n          </button>\n\n          <a\n            href={`http://localhost:8001/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '4px',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}\n          >\n            <span>🔍</span>\n            新窗口查看\n          </a>\n\n          <div style={{\n            fontSize: '12px',\n            color: '#aaa',\n            marginLeft: 'auto'\n          }}>\n            文件: {result.filename}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,UAAU,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EAChD,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgB,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACxD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC;IAAE4B,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC;IAAE4B,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChE,MAAMK,YAAY,GAAGjC,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMkC,gBAAgB,GAAGlC,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMmC,iBAAiB,GAAGnC,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMmC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF,MAAMC,OAAO,GAAG,MAAMnC,SAAS,CAAC,EAAE,CAAC;QACnCY,aAAa,CAACuB,OAAO,CAAC;MACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MACpC;IACF,CAAC;IACDF,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5B9B,cAAc,CAAC,IAAI,CAAC;IACpBE,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;EAED,MAAM6B,gBAAgB,GAAGA,CAAA,KAAM;IAC7B7B,aAAa,CAAC,IAAI,CAAC;IACnBF,cAAc,CAAC,KAAK,CAAC;EACvB,CAAC;EAED,MAAMgC,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACxCF,IAAI,CAACG,IAAI,GAAG,GAAGjC,UAAU,WAAWP,MAAM,CAACyC,YAAY,EAAE;IACzDJ,IAAI,CAACK,QAAQ,GAAG,YAAY1C,MAAM,CAAC2C,QAAQ,EAAE;IAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;IAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;IACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;EACjC,CAAC;;EAED;EACA,MAAMW,oBAAoB,GAAIC,CAAC,IAAK;IAClCnC,aAAa,CAAC,IAAI,CAAC;IACnBmC,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;EAED,MAAMC,oBAAoB,GAAIH,CAAC,IAAK;IAClC,IAAI,CAACpC,UAAU,IAAI,CAACgB,iBAAiB,CAACwB,OAAO,EAAE;IAE/C,MAAMC,IAAI,GAAGzB,iBAAiB,CAACwB,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC9D,MAAMlC,CAAC,GAAG4B,CAAC,CAACO,OAAO,GAAGF,IAAI,CAACG,IAAI;IAC/B,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAGxC,CAAC,GAAGiC,IAAI,CAACQ,KAAK,GAAI,GAAG,CAAC,CAAC;IACrElD,gBAAgB,CAAC8C,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/BjD,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC;;EAED;EACA,MAAMkD,WAAW,GAAIf,CAAC,IAAK;IACzB,IAAI,CAACpB,iBAAiB,CAACwB,OAAO,EAAE;IAEhCJ,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,MAAMe,KAAK,GAAGhB,CAAC,CAACiB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG;IACvC,MAAMC,OAAO,GAAGR,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE5C,SAAS,GAAGgD,KAAK,CAAC,CAAC;IAC7D/C,YAAY,CAACiD,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,cAAc,GAAInB,CAAC,IAAK;IAC5B,IAAIhC,SAAS,IAAI,CAAC,IAAIJ,UAAU,EAAE,OAAO,CAAC;;IAE1CW,YAAY,CAAC,IAAI,CAAC;IAClBE,eAAe,CAAC;MACdL,CAAC,EAAE4B,CAAC,CAACO,OAAO;MACZlC,CAAC,EAAE2B,CAAC,CAACoB;IACP,CAAC,CAAC;IACFpB,CAAC,CAACC,cAAc,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMoB,oBAAoB,GAAIrB,CAAC,IAAK;IAClC;IACA,IAAIA,CAAC,CAACsB,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,EAAE;MACzC,OAAO,CAAC;IACV;IAEA,IAAIvD,SAAS,GAAG,CAAC,IAAI,CAACJ,UAAU,EAAE;MAChCuD,cAAc,CAACnB,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,aAAa,GAAIxB,CAAC,IAAK;IAC3B,IAAI,CAAC1B,SAAS,EAAE;IAEhB,MAAMmD,MAAM,GAAGzB,CAAC,CAACO,OAAO,GAAG/B,YAAY,CAACJ,CAAC;IACzC,MAAM6C,MAAM,GAAGjB,CAAC,CAACoB,OAAO,GAAG5C,YAAY,CAACH,CAAC;IAEzCF,YAAY,CAACuD,IAAI,KAAK;MACpBtD,CAAC,EAAEsD,IAAI,CAACtD,CAAC,GAAGqD,MAAM;MAClBpD,CAAC,EAAEqD,IAAI,CAACrD,CAAC,GAAG4C;IACd,CAAC,CAAC,CAAC;IAEHxC,eAAe,CAAC;MACdL,CAAC,EAAE4B,CAAC,CAACO,OAAO;MACZlC,CAAC,EAAE2B,CAAC,CAACoB;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzBpD,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMqD,SAAS,GAAGA,CAAA,KAAM;IACtB3D,YAAY,CAAC,CAAC,CAAC;IACfE,YAAY,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMwD,MAAM,GAAGA,CAAA,KAAM;IACnB5D,YAAY,CAACyD,IAAI,IAAIhB,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEc,IAAI,GAAG,IAAI,CAAC,CAAC;EAChD,CAAC;EAED,MAAMI,OAAO,GAAGA,CAAA,KAAM;IACpB7D,YAAY,CAACyD,IAAI,IAAIhB,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEe,IAAI,GAAG,IAAI,CAAC,CAAC;EAClD,CAAC;;EAED;EACAhF,SAAS,CAAC,MAAM;IACd,IAAIkB,UAAU,EAAE;MACdyB,QAAQ,CAAC0C,gBAAgB,CAAC,WAAW,EAAE5B,oBAAoB,CAAC;MAC5Dd,QAAQ,CAAC0C,gBAAgB,CAAC,SAAS,EAAEjB,kBAAkB,CAAC;MACxD,OAAO,MAAM;QACXzB,QAAQ,CAAC2C,mBAAmB,CAAC,WAAW,EAAE7B,oBAAoB,CAAC;QAC/Dd,QAAQ,CAAC2C,mBAAmB,CAAC,SAAS,EAAElB,kBAAkB,CAAC;MAC7D,CAAC;IACH;EACF,CAAC,EAAE,CAAClD,UAAU,CAAC,CAAC;;EAEhB;EACAlB,SAAS,CAAC,MAAM;IACd,IAAI4B,SAAS,EAAE;MACbe,QAAQ,CAAC0C,gBAAgB,CAAC,WAAW,EAAEP,aAAa,CAAC;MACrDnC,QAAQ,CAAC0C,gBAAgB,CAAC,SAAS,EAAEJ,YAAY,CAAC;MAClD,OAAO,MAAM;QACXtC,QAAQ,CAAC2C,mBAAmB,CAAC,WAAW,EAAER,aAAa,CAAC;QACxDnC,QAAQ,CAAC2C,mBAAmB,CAAC,SAAS,EAAEL,YAAY,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAACrD,SAAS,EAAEE,YAAY,CAAC,CAAC;;EAI7B;EACA,MAAMyD,UAAU,GAAGA,CAAA,KAAM;IACvBtE,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC;EAED,oBACEd,OAAA;IAAKqF,KAAK,EAAE;MACVrB,KAAK,EAAE,MAAM;MACbsB,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,eAAe,EAAE,SAAS;MAC1BC,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBAEA3F,OAAA;MAAKqF,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BL,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBAEA3F,OAAA;QAAKqF,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEI,GAAG,EAAE;QAAO,CAAE;QAAAN,QAAA,gBACjE3F,OAAA;UAAKqF,KAAK,EAAE;YACVrB,KAAK,EAAE,KAAK;YACZsB,MAAM,EAAE,KAAK;YACbY,YAAY,EAAE,KAAK;YACnBT,eAAe,EAAE;UACnB;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACTtG,OAAA;UAAMqF,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,EAAC;QAAI;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC5DpG,MAAM,CAACsG,OAAO,iBACbxG,OAAA;UAAMqF,KAAK,EAAE;YAAEkB,QAAQ,EAAE,MAAM;YAAEb,KAAK,EAAE;UAAO,CAAE;UAAAC,QAAA,GAAC,SAAE,EAACzF,MAAM,CAACsG,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC3E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtG,OAAA;QAAKqF,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,KAAK;UAAEJ,UAAU,EAAE;QAAS,CAAE;QAAAF,QAAA,gBAChE3F,OAAA;UACEyG,OAAO,EAAEA,CAAA,KAAMvF,WAAW,CAAC,OAAO,CAAE;UACpCmE,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAExE,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,MAAM;YAC1DyE,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtG,OAAA;UACEyG,OAAO,EAAEA,CAAA,KAAMvF,WAAW,CAAC,cAAc,CAAE;UAC3CmE,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAExE,QAAQ,KAAK,cAAc,GAAG,SAAS,GAAG,MAAM;YACjEyE,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRrF,QAAQ,KAAK,OAAO,iBACnBjB,OAAA;UACEyG,OAAO,EAAErB,UAAW;UACpBC,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdgB,MAAM,EAAE,MAAM;YACdR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLpG,MAAM,CAAC2G,WAAW,iBACjB7G,OAAA;MAAKqF,KAAK,EAAE;QACVC,MAAM,EAAE3E,UAAU,GAAG,MAAM,GAAG,MAAM;QACpC8E,eAAe,EAAE,SAAS;QAC1BG,YAAY,EAAE,gBAAgB;QAC9BG,OAAO,EAAE,UAAU;QACnBC,UAAU,EAAE,CAAC;QACbY,UAAU,EAAE;MACd,CAAE;MAAAjB,QAAA,gBACA3F,OAAA;QAAKqF,KAAK,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEM,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE;QAAgB,CAAE;QAAAH,QAAA,gBACrF3F,OAAA;UAAKqF,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEM,UAAU,EAAE,QAAQ;YAAEI,GAAG,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACjE3F,OAAA;YAAMqF,KAAK,EAAE;cAAEkB,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAC7D,CAAC3F,UAAU,iBACVX,OAAA;YAAKqF,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEU,GAAG,EAAE,MAAM;cAAEM,QAAQ,EAAE,MAAM;cAAEb,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,gBAC5E3F,OAAA;cAAA2F,QAAA,GAAOzF,MAAM,CAAC2G,WAAW,CAACC,KAAK,EAAC,eAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CtG,OAAA;cAAA2F,QAAA,EAAOzF,MAAM,CAAC2G,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG;YAAI;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtEtG,OAAA;cAAA2F,QAAA,GAAM,cAAE,EAAC,CAACzF,MAAM,CAAC2G,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClEtG,OAAA;cAAA2F,QAAA,GAAM,cAAE,EAAC,CAACzF,MAAM,CAAC2G,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNtG,OAAA;UACEyG,OAAO,EAAEA,CAAA,KAAM7F,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1C0E,KAAK,EAAE;YACLU,OAAO,EAAE,SAAS;YAClBN,eAAe,EAAE,aAAa;YAC9BC,KAAK,EAAE,MAAM;YACbgB,MAAM,EAAE,gBAAgB;YACxBR,YAAY,EAAE,KAAK;YACnBS,MAAM,EAAE,SAAS;YACjBJ,QAAQ,EAAE,MAAM;YAChBK,UAAU,EAAE;UACd,CAAE;UACFO,YAAY,EAAGhE,CAAC,IAAK;YACnBA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,MAAM;YACvCtC,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UACF0B,YAAY,EAAGjE,CAAC,IAAK;YACnBA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,aAAa;YAC9CtC,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACK,KAAK,GAAG,MAAM;UAC/B,CAAE;UAAAC,QAAA,EAEDhF,UAAU,GAAG,GAAG,GAAG;QAAG;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAEL3F,UAAU,iBACTX,OAAA;QAAKqF,KAAK,EAAE;UACVgC,SAAS,EAAE,MAAM;UACjB9B,OAAO,EAAE,MAAM;UACf+B,mBAAmB,EAAE,sCAAsC;UAC3DrB,GAAG,EAAE,KAAK;UACVM,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE;QACT,CAAE;QAAAC,QAAA,gBACA3F,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACpG,MAAM,CAAC2G,WAAW,CAACC,KAAK,EAAC,GAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnFtG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAK;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACpG,MAAM,CAAC2G,WAAW,CAACE,cAAc,GAAG,YAAY,GAAG,MAAM;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnHtG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAACpG,MAAM,CAAC2G,WAAW,CAACG,UAAU,GAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzGtG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACpG,MAAM,CAAC2G,WAAW,CAACU,SAAS;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpFtG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACpG,MAAM,CAAC2G,WAAW,CAACW,UAAU,CAACP,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGtG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAI;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACpG,MAAM,CAAC2G,WAAW,CAACY,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/FtG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAACpG,MAAM,CAAC2G,WAAW,CAACa,UAAU,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,EAAExH,MAAM,CAAC2G,WAAW,CAACa,UAAU;QAAA;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnItG,OAAA;UAAA2F,QAAA,gBAAK3F,OAAA;YAAMqF,KAAK,EAAE;cAAEK,KAAK,EAAE;YAAO,CAAE;YAAAC,QAAA,EAAC;UAAG;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,KAAC,EAAC,CAACpG,MAAM,CAAC2G,WAAW,CAACK,MAAM,GAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClG,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN,eAGDtG,OAAA;MAAKqF,KAAK,EAAE;QACVsC,IAAI,EAAE,CAAC;QACPpC,OAAO,EAAE,MAAM;QACfC,aAAa,EAAE,QAAQ;QACvBoC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAE;MAAAlC,QAAA,EACC1E,QAAQ,KAAK,OAAO;MAAA;MACnB;MACAjB,OAAA;QAAKqF,KAAK,EAAE;UACVsC,IAAI,EAAE,CAAC;UACPpC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEA3F,OAAA;UAAKqF,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BgC,YAAY,EAAE,MAAM;YACpBvB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,gBACA3F,OAAA;YAAKqF,KAAK,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAEM,UAAU,EAAE,QAAQ;cAAEI,GAAG,EAAE;YAAM,CAAE;YAAAN,QAAA,eAChE3F,OAAA;cAAA2F,QAAA,GAAM,0HAAyB,EAAC9E,aAAa,CAACoG,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAGNtG,OAAA;YAAKqF,KAAK,EAAE;cACVE,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE,KAAK;cACV4B,QAAQ,EAAE,UAAU;cACpBE,MAAM,EAAE,IAAI,CAAC;YACf,CAAE;YAAApC,QAAA,gBACA3F,OAAA;cACEyG,OAAO,EAAExB,OAAQ;cACjB+C,QAAQ,EAAE7G,SAAS,IAAI,GAAI;cAC3BkE,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAEtE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnDuE,KAAK,EAAEvE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACzCuF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAExF,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS;gBACpDoF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETtG,OAAA;cAAMqF,KAAK,EAAE;gBACX6C,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,QAAQ;gBACnB5B,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAEvE,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG;cACpC,CAAE;cAAAwE,QAAA,GACC,CAACxE,SAAS,GAAG,GAAG,EAAE8F,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPtG,OAAA;cACEyG,OAAO,EAAEzB,MAAO;cAChBgD,QAAQ,EAAE7G,SAAS,IAAI,CAAE;cACzBkE,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAEtE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACjDuE,KAAK,EAAEvE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACvCuF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAExF,SAAS,IAAI,CAAC,GAAG,aAAa,GAAG,SAAS;gBAClDoF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETtG,OAAA;cACEyG,OAAO,EAAE1B,SAAU;cACnBiD,QAAQ,EAAE7G,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAE;cACpE6D,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAGtE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBAC9FkE,KAAK,EAAGvE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBACpFkF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAGxF,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,aAAa,GAAG,SAAS;gBAC/F+E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,0BAAM;cAAAtC,QAAA,EACb;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtG,OAAA;UACEoI,GAAG,EAAEvG,YAAa;UAClBwD,KAAK,EAAE;YACLwC,QAAQ,EAAE,UAAU;YACpB7D,KAAK,EAAE,MAAM;YACbsB,MAAM,EAAE,MAAM;YACdqB,MAAM,EAAE5F,UAAU,GAAG,WAAW,GAAG,SAAS;YAC5CsH,UAAU,EAAE,MAAM;YAClB9C,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB8B,QAAQ,EAAE,MAAM,CAAC;UACnB,CAAE;UAAAjC,QAAA,EAEDxF,aAAa,iBACZH,OAAA;YAAKqF,KAAK,EAAE;cACVwC,QAAQ,EAAE,UAAU;cACpB7D,KAAK,EAAE,MAAM;cACbsB,MAAM,EAAE,MAAM;cACdgD,SAAS,EAAE,qBAAqB;cAChC/C,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAH,QAAA,eAEA3F,OAAA;cACEoI,GAAG,EAAErG,iBAAkB;cACvBsD,KAAK,EAAE;gBACLwC,QAAQ,EAAE,UAAU;gBACpBtC,OAAO,EAAE,cAAc;gBACvBmB,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnB0B,QAAQ,EAAE,QAAQ;gBAClBW,SAAS,EAAE,SAASpH,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtGqH,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAEnF,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1DkF,MAAM,EAAExF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACFgH,OAAO,EAAEvE,WAAY;cACrBwE,WAAW,EAAElE,oBAAqB;cAAAmB,QAAA,GAGjCpF,UAAU,gBACTP,OAAA;gBAAKqF,KAAK,EAAE;kBACVrB,KAAK,EAAE,OAAO;kBACdsB,MAAM,EAAE,OAAO;kBACfC,OAAO,EAAE,MAAM;kBACfM,UAAU,EAAE,QAAQ;kBACpBC,cAAc,EAAE,QAAQ;kBACxBJ,KAAK,EAAE,SAAS;kBAChBD,eAAe,EAAE;gBACnB,CAAE;gBAAAE,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,gBAENtG,OAAA;gBACEoI,GAAG,EAAEtG,gBAAiB;gBACtB6G,GAAG,EAAE,gCAAgCzI,MAAM,CAACyC,YAAY,MAAMiG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAE3G,eAAgB;gBACxB4G,OAAO,EAAE3G,gBAAiB;gBAC1BgD,KAAK,EAAE;kBACL4D,QAAQ,EAAE,MAAM;kBAChBX,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdC,OAAO,EAAE,OAAO;kBAChB2D,OAAO,EAAE7I,WAAW,GAAG,CAAC,GAAG,GAAG;kBAC9B8I,cAAc,EAAE,MAAM,CAAC;gBACzB;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF,EAGAjG,WAAW,iBACVL,OAAA;gBACEqF,KAAK,EAAE;kBACLwC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,CAAC;kBACNzF,IAAI,EAAE,CAAC;kBACPK,KAAK,EAAE,GAAGnD,aAAa,GAAG;kBAC1ByE,MAAM,EAAE,MAAM;kBACdsC,QAAQ,EAAE;gBACZ,CAAE;gBAAAjC,QAAA,eAEF3F,OAAA;kBACE2I,GAAG,EAAExI,aAAc;kBACnB2I,GAAG,EAAC,0BAAM;kBACVzD,KAAK,EAAE;oBACL;oBACArB,KAAK,EAAE,GAAG,GAAG,GAAG,GAAG,GAAGnD,aAAa,GAAG;oBACtCyE,MAAM,EAAE,MAAM;oBACd+D,SAAS,EAAE,MAAM;oBAAE;oBACnBF,cAAc,EAAE,WAAW;oBAAE;oBAC7BtB,QAAQ,EAAE,UAAU;oBACpBlE,IAAI,EAAE,CAAC;oBACPyF,GAAG,EAAE;kBACP;gBAAE;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EAGAjG,WAAW,iBACVL,OAAA;gBACE,mBAAgB,MAAM;gBACtBqF,KAAK,EAAE;kBACLwC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,CAAC;kBACNzF,IAAI,EAAE,GAAG9C,aAAa,GAAG;kBACzBmD,KAAK,EAAE,KAAK;kBACZsB,MAAM,EAAE,MAAM;kBACdG,eAAe,EAAE,SAAS;kBAC1BkB,MAAM,EAAE,WAAW;kBACnB2C,SAAS,EAAE,iCAAiC;kBAC5Cf,SAAS,EAAE,kBAAkB;kBAC7BR,MAAM,EAAE;gBACV,CAAE;gBACFW,WAAW,EAAExF,oBAAqB;gBAAAyC,QAAA,eAGlC3F,OAAA;kBACEqF,KAAK,EAAE;oBACLwC,QAAQ,EAAE,UAAU;oBACpBuB,GAAG,EAAE,KAAK;oBACVzF,IAAI,EAAE,KAAK;oBACX4E,SAAS,EAAE,uBAAuB;oBAClCvE,KAAK,EAAE,MAAM;oBACbsB,MAAM,EAAE,MAAM;oBACdG,eAAe,EAAE,SAAS;oBAC1BS,YAAY,EAAE,KAAK;oBACnBX,OAAO,EAAE,MAAM;oBACfM,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,QAAQ;oBACxBJ,KAAK,EAAE,OAAO;oBACda,QAAQ,EAAE,MAAM;oBAChBgD,UAAU,EAAE,MAAM;oBAClBD,SAAS,EAAE;kBACb,CAAE;kBAAA3D,QAAA,EACH;gBAED;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAACjG,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;gBAAKqF,KAAK,EAAE;kBACVwC,QAAQ,EAAE,UAAU;kBACpBuB,GAAG,EAAE,KAAK;kBACVzF,IAAI,EAAE,KAAK;kBACX4E,SAAS,EAAE,uBAAuB;kBAClC7C,KAAK,EAAE,MAAM;kBACbD,eAAe,EAAE,oBAAoB;kBACrCM,OAAO,EAAE,UAAU;kBACnBG,YAAY,EAAE,KAAK;kBACnBK,QAAQ,EAAE;gBACZ,CAAE;gBAAAZ,QAAA,EAAC;cAEH;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;MAAA;MAEN;MACAtG,OAAA;QAAKqF,KAAK,EAAE;UACVsC,IAAI,EAAE,CAAC;UACPpC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBO,OAAO,EAAE;QACX,CAAE;QAAAJ,QAAA,gBAEA3F,OAAA;UAAKqF,KAAK,EAAE;YACVC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE,MAAM;YACfM,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,eAAe;YAC/BgC,YAAY,EAAE,MAAM;YACpBvB,QAAQ,EAAE,MAAM;YAChBb,KAAK,EAAE;UACT,CAAE;UAAAC,QAAA,gBACA3F,OAAA;YAAA2F,QAAA,EAAM;UAAM;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGnBtG,OAAA;YAAKqF,KAAK,EAAE;cACVE,OAAO,EAAE,MAAM;cACfM,UAAU,EAAE,QAAQ;cACpBI,GAAG,EAAE,KAAK;cACV4B,QAAQ,EAAE,UAAU;cACpBE,MAAM,EAAE,IAAI,CAAC;YACf,CAAE;YAAApC,QAAA,gBACA3F,OAAA;cACEyG,OAAO,EAAExB,OAAQ;cACjB+C,QAAQ,EAAE7G,SAAS,IAAI,GAAI;cAC3BkE,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAEtE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACnDuE,KAAK,EAAEvE,SAAS,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM;gBACzCuF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAExF,SAAS,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS;gBACpDoF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETtG,OAAA;cAAMqF,KAAK,EAAE;gBACX6C,QAAQ,EAAE,MAAM;gBAChBC,SAAS,EAAE,QAAQ;gBACnB5B,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAEvE,SAAS,KAAK,CAAC,GAAG,MAAM,GAAG;cACpC,CAAE;cAAAwE,QAAA,GACC,CAACxE,SAAS,GAAG,GAAG,EAAE8F,OAAO,CAAC,CAAC,CAAC,EAAC,GAChC;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPtG,OAAA;cACEyG,OAAO,EAAEzB,MAAO;cAChBgD,QAAQ,EAAE7G,SAAS,IAAI,CAAE;cACzBkE,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAEtE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACjDuE,KAAK,EAAEvE,SAAS,IAAI,CAAC,GAAG,MAAM,GAAG,MAAM;gBACvCuF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAExF,SAAS,IAAI,CAAC,GAAG,aAAa,GAAG,SAAS;gBAClDoF,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,cAAI;cAAAtC,QAAA,EACX;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETtG,OAAA;cACEyG,OAAO,EAAE1B,SAAU;cACnBiD,QAAQ,EAAE7G,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAE;cACpE6D,KAAK,EAAE;gBACLrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,MAAM;gBACdG,eAAe,EAAGtE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBAC9FkE,KAAK,EAAGvE,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,MAAM,GAAG,MAAM;gBACpFkF,MAAM,EAAE,gBAAgB;gBACxBR,YAAY,EAAE,KAAK;gBACnBS,MAAM,EAAGxF,SAAS,KAAK,CAAC,IAAIE,SAAS,CAACE,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACG,CAAC,KAAK,CAAC,GAAI,aAAa,GAAG,SAAS;gBAC/F+E,QAAQ,EAAE,MAAM;gBAChBhB,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+B,QAAQ,EAAE,UAAU;gBACpBE,MAAM,EAAE;cACV,CAAE;cACFE,KAAK,EAAC,0BAAM;cAAAtC,QAAA,EACb;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtG,OAAA;UAAKqF,KAAK,EAAE;YACVsC,IAAI,EAAE,CAAC;YACPpC,OAAO,EAAE,MAAM;YACfU,GAAG,EAAE,MAAM;YACXJ,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB8B,QAAQ,EAAE,MAAM,CAAC;UACnB,CAAE;UAAAjC,QAAA,GAEDxF,aAAa,iBACZH,OAAA;YAAKqF,KAAK,EAAE;cACVsC,IAAI,EAAE,GAAG;cACTQ,SAAS,EAAE,QAAQ;cACnBc,QAAQ,EAAE;YACZ,CAAE;YAAAtD,QAAA,gBACA3F,OAAA;cAAKqF,KAAK,EAAE;gBACVyC,YAAY,EAAE,KAAK;gBACnBvB,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNtG,OAAA;cACEqF,KAAK,EAAE;gBACLE,OAAO,EAAE,cAAc;gBACvBgD,SAAS,EAAE,SAASpH,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtGqH,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAEnF,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1DkF,MAAM,EAAExF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACFgH,OAAO,EAAEvE,WAAY;cACrBwE,WAAW,EAAElE,oBAAqB;cAAAmB,QAAA,eAElC3F,OAAA;gBACE2I,GAAG,EAAExI,aAAc;gBACnB2I,GAAG,EAAC,0BAAM;gBACVzD,KAAK,EAAE;kBACL4D,QAAQ,EAAE,MAAM;kBAChBX,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE,mBAAmB;kBAC3BR,YAAY,EAAE,KAAK;kBACnBmD,SAAS,EAAE,SAAS;kBACpBF,cAAc,EAAE,WAAW,CAAC;gBAC9B;cAAE;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDtG,OAAA;YAAKqF,KAAK,EAAE;cACVsC,IAAI,EAAE,GAAG;cACTQ,SAAS,EAAE,QAAQ;cACnBc,QAAQ,EAAE;YACZ,CAAE;YAAAtD,QAAA,gBACA3F,OAAA;cAAKqF,KAAK,EAAE;gBACVyC,YAAY,EAAE,KAAK;gBACnBvB,QAAQ,EAAE,MAAM;gBAChBb,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAEL,CAACjG,WAAW,IAAI,CAACE,UAAU,iBAC1BP,OAAA;cAAKqF,KAAK,EAAE;gBACVrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,OAAO;gBACfC,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE,iBAAiB;gBACzBR,YAAY,EAAE,KAAK;gBACnBR,KAAK,EAAE,MAAM;gBACbD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,EAEA/F,UAAU,iBACTP,OAAA;cAAKqF,KAAK,EAAE;gBACVrB,KAAK,EAAE,MAAM;gBACbsB,MAAM,EAAE,OAAO;gBACfC,OAAO,EAAE,MAAM;gBACfM,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBY,MAAM,EAAE,mBAAmB;gBAC3BR,YAAY,EAAE,KAAK;gBACnBR,KAAK,EAAE,SAAS;gBAChBD,eAAe,EAAE;cACnB,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN,eAEDtG,OAAA;cACEqF,KAAK,EAAE;gBACLE,OAAO,EAAElF,WAAW,GAAG,cAAc,GAAG,MAAM;gBAC9CkI,SAAS,EAAE,SAASpH,SAAS,eAAeE,SAAS,CAACE,CAAC,GAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,GAAGL,SAAS,KAAK;gBACtGqH,eAAe,EAAE,eAAe;gBAChC5B,UAAU,EAAEnF,SAAS,GAAG,MAAM,GAAG,yBAAyB;gBAC1DkF,MAAM,EAAExF,SAAS,GAAG,CAAC,GAAIM,SAAS,GAAG,UAAU,GAAG,MAAM,GAAI;cAC9D,CAAE;cACFgH,OAAO,EAAEvE,WAAY;cACrBwE,WAAW,EAAElE,oBAAqB;cAAAmB,QAAA,eAElC3F,OAAA;gBACE2I,GAAG,EAAE,gCAAgCzI,MAAM,CAACyC,YAAY,MAAMiG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG;gBAC3EC,GAAG,EAAC,0BAAM;gBACVC,MAAM,EAAE3G,eAAgB;gBACxB4G,OAAO,EAAE3G,gBAAiB;gBAC1BgD,KAAK,EAAE;kBACL4D,QAAQ,EAAE,MAAM;kBAChBX,SAAS,EAAE,qBAAqB;kBAChChD,MAAM,EAAE,MAAM;kBACdoB,MAAM,EAAE,mBAAmB;kBAC3BR,YAAY,EAAE,KAAK;kBACnBmD,SAAS,EAAE;gBACb;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLjG,WAAW,iBACVL,OAAA;MAAKqF,KAAK,EAAE;QACVC,MAAM,EAAE,MAAM;QACdG,eAAe,EAAE,MAAM;QACvB+D,SAAS,EAAE,gBAAgB;QAC3BjE,OAAO,EAAE,MAAM;QACfM,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBG,GAAG,EAAE,MAAM;QACXF,OAAO,EAAE,QAAQ;QACjBC,UAAU,EAAE;MACd,CAAE;MAAAL,QAAA,gBACA3F,OAAA;QACEyG,OAAO,EAAEnE,aAAc;QACvB+C,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgB,MAAM,EAAE,MAAM;UACdR,YAAY,EAAE,KAAK;UACnBS,MAAM,EAAE,SAAS;UACjBJ,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGhE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE2B,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhE3F,OAAA;UAAA2F,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,4BAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETtG,OAAA;QACE0C,IAAI,EAAE,gCAAgCxC,MAAM,CAACyC,YAAY,EAAG;QAC5D8B,MAAM,EAAC,QAAQ;QACfgF,GAAG,EAAC,qBAAqB;QACzBpE,KAAK,EAAE;UACLU,OAAO,EAAE,UAAU;UACnBN,eAAe,EAAE,SAAS;UAC1BC,KAAK,EAAE,OAAO;UACdgE,cAAc,EAAE,MAAM;UACtBxD,YAAY,EAAE,KAAK;UACnBK,QAAQ,EAAE,MAAM;UAChBhB,OAAO,EAAE,MAAM;UACfM,UAAU,EAAE,QAAQ;UACpBI,GAAG,EAAE,KAAK;UACVW,UAAU,EAAE;QACd,CAAE;QACFO,YAAY,EAAGhE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAChE2B,YAAY,EAAGjE,CAAC,IAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,GAAG,SAAU;QAAAE,QAAA,gBAEhE3F,OAAA;UAAA2F,QAAA,EAAM;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,kCAEjB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAEJtG,OAAA;QAAKqF,KAAK,EAAE;UACVkB,QAAQ,EAAE,MAAM;UAChBb,KAAK,EAAE,MAAM;UACbiE,UAAU,EAAE;QACd,CAAE;QAAAhE,QAAA,GAAC,gBACG,EAACzF,MAAM,CAAC2C,QAAQ;MAAA;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAClG,EAAA,CAj5BIH,UAAU;AAAA2J,EAAA,GAAV3J,UAAU;AAm5BhB,eAAeA,UAAU;AAAC,IAAA2J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}