{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5E94\\u7528 - \\u8C03\\u8BD5\\u7248\\u672C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\u5982\\u679C\\u60A8\\u770B\\u5230\\u8FD9\\u4E2A\\u9875\\u9762\\uFF0C\\u8BF4\\u660EReact\\u5E94\\u7528\\u6B63\\u5728\\u6B63\\u5E38\\u8FD0\\u884C\\u3002\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f0f0f0',\n        padding: '15px',\n        borderRadius: '5px',\n        margin: '20px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u8C03\\u8BD5\\u4FE1\\u606F:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"React\\u7248\\u672C: \", React.version]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u5F53\\u524D\\u65F6\\u95F4: \", new Date().toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u9875\\u9762URL: \", window.location.href]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => alert('按钮点击正常工作！'),\n      style: {\n        padding: '10px 20px',\n        backgroundColor: '#007bff',\n        color: 'white',\n        border: 'none',\n        borderRadius: '4px',\n        cursor: 'pointer'\n      },\n      children: \"\\u6D4B\\u8BD5\\u6309\\u94AE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "App", "style", "padding", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "borderRadius", "margin", "version", "Date", "toLocaleString", "window", "location", "href", "onClick", "alert", "color", "border", "cursor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\n\nfunction App() {\n  return (\n    <div style={{ \n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    }}>\n      <h1>图像增强应用 - 调试版本</h1>\n      <p>如果您看到这个页面，说明React应用正在正常运行。</p>\n      \n      <div style={{\n        backgroundColor: '#f0f0f0',\n        padding: '15px',\n        borderRadius: '5px',\n        margin: '20px 0'\n      }}>\n        <h3>调试信息:</h3>\n        <ul>\n          <li>React版本: {React.version}</li>\n          <li>当前时间: {new Date().toLocaleString()}</li>\n          <li>页面URL: {window.location.href}</li>\n        </ul>\n      </div>\n      \n      <button \n        onClick={() => alert('按钮点击正常工作！')}\n        style={{\n          padding: '10px 20px',\n          backgroundColor: '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: 'pointer'\n        }}\n      >\n        测试按钮\n      </button>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACAL,OAAA;MAAAK,QAAA,EAAI;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACtBT,OAAA;MAAAK,QAAA,EAAG;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEjCT,OAAA;MAAKE,KAAK,EAAE;QACVQ,eAAe,EAAE,SAAS;QAC1BP,OAAO,EAAE,MAAM;QACfQ,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,gBACAL,OAAA;QAAAK,QAAA,EAAI;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdT,OAAA;QAAAK,QAAA,gBACEL,OAAA;UAAAK,QAAA,GAAI,qBAAS,EAACP,KAAK,CAACe,OAAO;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACjCT,OAAA;UAAAK,QAAA,GAAI,4BAAM,EAAC,IAAIS,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5CT,OAAA;UAAAK,QAAA,GAAI,mBAAO,EAACW,MAAM,CAACC,QAAQ,CAACC,IAAI;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENT,OAAA;MACEmB,OAAO,EAAEA,CAAA,KAAMC,KAAK,CAAC,WAAW,CAAE;MAClClB,KAAK,EAAE;QACLC,OAAO,EAAE,WAAW;QACpBO,eAAe,EAAE,SAAS;QAC1BW,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdX,YAAY,EAAE,KAAK;QACnBY,MAAM,EAAE;MACV,CAAE;MAAAlB,QAAA,EACH;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACe,EAAA,GAtCQvB,GAAG;AAwCZ,eAAeA,GAAG;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}