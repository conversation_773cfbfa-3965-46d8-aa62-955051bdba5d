{"ast": null, "code": "import React,{useState,useRef}from'react';import UploadForm from'./UploadForm';import ResultView from'./ResultView';import{getApiUrl}from'./config/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[result,setResult]=useState(null);const[isLoading,setIsLoading]=useState(false);const[error,setError]=useState(null);const[originalImage,setOriginalImage]=useState(null);const uploadFormRef=useRef(null);const handleUpload=async formData=>{setIsLoading(true);setError(null);// 保存原始图像用于对比\nconst file=formData.get('file');if(file){const reader=new FileReader();reader.onload=e=>setOriginalImage(e.target.result);reader.readAsDataURL(file);}try{// 使用自动发现的API地址\nconst apiUrl=await getApiUrl('/enhance/');const response=await fetch(apiUrl,{method:'POST',body:formData});if(!response.ok){const errorData=await response.json();throw new Error(errorData.detail||'处理失败');}const data=await response.json();setResult(data);}catch(err){console.error('上传失败:',err);setError(err.message||'网络错误，请检查后端服务是否正常运行');}finally{setIsLoading(false);}};const handleReset=()=>{setResult(null);setError(null);setOriginalImage(null);};const handleApply=()=>{if(uploadFormRef.current){uploadFormRef.current.triggerSubmit();}};return/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',height:'100vh',backgroundColor:'#1a1a1a',color:'#fff',fontFamily:'-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'},children:[/*#__PURE__*/_jsx(\"div\",{style:{flex:1,display:'flex',flexDirection:'column',minWidth:0},children:/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,overflow:'hidden',display:'flex',flexDirection:'column'},children:[error&&/*#__PURE__*/_jsx(\"div\",{style:{padding:'12px',backgroundColor:'#ff4444',color:'#fff',margin:'8px',borderRadius:'4px',fontSize:'14px'},children:error}),/*#__PURE__*/_jsx(ResultView,{result:result,originalImage:originalImage,isLoading:isLoading})]})}),/*#__PURE__*/_jsxs(\"div\",{style:{width:'320px',backgroundColor:'#2b2b2b',borderLeft:'1px solid #444',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{padding:'12px 16px',borderBottom:'1px solid #444',backgroundColor:'#333',display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',fontWeight:'500',color:'#fff'},children:\"\\u56FE\\u50CF\\u589E\\u5F3A\\u53C2\\u6570\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'8px'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleReset,style:{padding:'6px 12px',backgroundColor:'#555',color:'#fff',border:'none',borderRadius:'4px',fontSize:'12px',cursor:'pointer'},children:\"\\u91CD\\u7F6E\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleApply,disabled:isLoading,style:{padding:'6px 12px',backgroundColor:isLoading?'#666':'#4a90e2',color:'#fff',border:'none',borderRadius:'4px',fontSize:'12px',cursor:isLoading?'not-allowed':'pointer',position:'relative',overflow:'hidden'},children:[isLoading&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,background:'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',animation:'loading 1.5s infinite'}}),isLoading?'处理中...':'应用']})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,overflow:'auto',padding:'16px'},children:/*#__PURE__*/_jsx(UploadForm,{ref:uploadFormRef,onUpload:handleUpload,isLoading:isLoading})})]}),/*#__PURE__*/_jsx(\"style\",{children:`\n        @keyframes loading {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n      `})]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useRef", "UploadForm", "ResultView", "getApiUrl", "jsx", "_jsx", "jsxs", "_jsxs", "App", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "uploadFormRef", "handleUpload", "formData", "file", "get", "reader", "FileReader", "onload", "e", "target", "readAsDataURL", "apiUrl", "response", "fetch", "method", "body", "ok", "errorData", "json", "Error", "detail", "data", "err", "console", "message", "handleReset", "handleApply", "current", "triggerSubmit", "style", "display", "height", "backgroundColor", "color", "fontFamily", "children", "flex", "flexDirection", "min<PERSON><PERSON><PERSON>", "overflow", "padding", "margin", "borderRadius", "fontSize", "width", "borderLeft", "borderBottom", "alignItems", "justifyContent", "fontWeight", "gap", "onClick", "border", "cursor", "disabled", "position", "top", "left", "right", "bottom", "background", "animation", "ref", "onUpload"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport UploadForm from './UploadForm';\nimport ResultView from './ResultView';\nimport { getApiUrl } from './config/api';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const uploadFormRef = useRef(null);\n\n  const handleUpload = async (formData) => {\n    setIsLoading(true);\n    setError(null);\n    \n    // 保存原始图像用于对比\n    const file = formData.get('file');\n    if (file) {\n      const reader = new FileReader();\n      reader.onload = (e) => setOriginalImage(e.target.result);\n      reader.readAsDataURL(file);\n    }\n\n    try {\n      // 使用自动发现的API地址\n      const apiUrl = await getApiUrl('/enhance/');\n      const response = await fetch(apiUrl, {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.detail || '处理失败');\n      }\n\n      const data = await response.json();\n      setResult(data);\n    } catch (err) {\n      console.error('上传失败:', err);\n      setError(err.message || '网络错误，请检查后端服务是否正常运行');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleReset = () => {\n    setResult(null);\n    setError(null);\n    setOriginalImage(null);\n  };\n\n  const handleApply = () => {\n    if (uploadFormRef.current) {\n      uploadFormRef.current.triggerSubmit();\n    }\n  };\n\n  return (\n    <div style={{ \n      display: 'flex', \n      height: '100vh', \n      backgroundColor: '#1a1a1a',\n      color: '#fff',\n      fontFamily: '-apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif'\n    }}>\n      {/* 主工作区 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        minWidth: 0\n      }}>\n        {/* 结果展示区域 */}\n        <div style={{ \n          flex: 1, \n          overflow: 'hidden',\n          display: 'flex',\n          flexDirection: 'column'\n        }}>\n          {error && (\n            <div style={{\n              padding: '12px',\n              backgroundColor: '#ff4444',\n              color: '#fff',\n              margin: '8px',\n              borderRadius: '4px',\n              fontSize: '14px'\n            }}>\n              {error}\n            </div>\n          )}\n          \n          <ResultView \n            result={result} \n            originalImage={originalImage}\n            isLoading={isLoading}\n          />\n        </div>\n      </div>\n\n      {/* 右侧参数面板 */}\n      <div style={{ \n        width: '320px', \n        backgroundColor: '#2b2b2b',\n        borderLeft: '1px solid #444',\n        display: 'flex',\n        flexDirection: 'column'\n      }}>\n        {/* 顶部工具栏 */}\n        <div style={{\n          padding: '12px 16px',\n          borderBottom: '1px solid #444',\n          backgroundColor: '#333',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        }}>\n          <div style={{ \n            fontSize: '14px', \n            fontWeight: '500',\n            color: '#fff'\n          }}>\n            图像增强参数\n          </div>\n          \n          <div style={{ display: 'flex', gap: '8px' }}>\n            <button\n              onClick={handleReset}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: '#555',\n                color: '#fff',\n                border: 'none',\n                borderRadius: '4px',\n                fontSize: '12px',\n                cursor: 'pointer'\n              }}\n            >\n              重置\n            </button>\n            <button\n              onClick={handleApply}\n              disabled={isLoading}\n              style={{\n                padding: '6px 12px',\n                backgroundColor: isLoading ? '#666' : '#4a90e2',\n                color: '#fff',\n                border: 'none',\n                borderRadius: '4px',\n                fontSize: '12px',\n                cursor: isLoading ? 'not-allowed' : 'pointer',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n            >\n              {isLoading && (\n                <div style={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  right: 0,\n                  bottom: 0,\n                  background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',\n                  animation: 'loading 1.5s infinite'\n                }} />\n              )}\n              {isLoading ? '处理中...' : '应用'}\n            </button>\n          </div>\n        </div>\n\n        {/* 参数配置区域 */}\n        <div style={{ \n          flex: 1, \n          overflow: 'auto',\n          padding: '16px'\n        }}>\n          <UploadForm \n            ref={uploadFormRef}\n            onUpload={handleUpload} \n            isLoading={isLoading}\n          />\n        </div>\n      </div>\n\n      {/* CSS动画 */}\n      <style>{`\n        @keyframes loading {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC/C,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,MAAO,CAAAC,UAAU,KAAM,cAAc,CACrC,OAASC,SAAS,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,MAAM,CAAEC,SAAS,CAAC,CAAGX,QAAQ,CAAC,IAAI,CAAC,CAC1C,KAAM,CAACY,SAAS,CAAEC,YAAY,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACc,KAAK,CAAEC,QAAQ,CAAC,CAAGf,QAAQ,CAAC,IAAI,CAAC,CACxC,KAAM,CAACgB,aAAa,CAAEC,gBAAgB,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAAAkB,aAAa,CAAGjB,MAAM,CAAC,IAAI,CAAC,CAElC,KAAM,CAAAkB,YAAY,CAAG,KAAO,CAAAC,QAAQ,EAAK,CACvCP,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,KAAM,CAAAM,IAAI,CAAGD,QAAQ,CAACE,GAAG,CAAC,MAAM,CAAC,CACjC,GAAID,IAAI,CAAE,CACR,KAAM,CAAAE,MAAM,CAAG,GAAI,CAAAC,UAAU,CAAC,CAAC,CAC/BD,MAAM,CAACE,MAAM,CAAIC,CAAC,EAAKT,gBAAgB,CAACS,CAAC,CAACC,MAAM,CAACjB,MAAM,CAAC,CACxDa,MAAM,CAACK,aAAa,CAACP,IAAI,CAAC,CAC5B,CAEA,GAAI,CACF;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAzB,SAAS,CAAC,WAAW,CAAC,CAC3C,KAAM,CAAA0B,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACF,MAAM,CAAE,CACnCG,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEb,QACR,CAAC,CAAC,CAEF,GAAI,CAACU,QAAQ,CAACI,EAAE,CAAE,CAChB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CACvC,KAAM,IAAI,CAAAC,KAAK,CAACF,SAAS,CAACG,MAAM,EAAI,MAAM,CAAC,CAC7C,CAEA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAT,QAAQ,CAACM,IAAI,CAAC,CAAC,CAClCzB,SAAS,CAAC4B,IAAI,CAAC,CACjB,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAAC3B,KAAK,CAAC,OAAO,CAAE0B,GAAG,CAAC,CAC3BzB,QAAQ,CAACyB,GAAG,CAACE,OAAO,EAAI,oBAAoB,CAAC,CAC/C,CAAC,OAAS,CACR7B,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA8B,WAAW,CAAGA,CAAA,GAAM,CACxBhC,SAAS,CAAC,IAAI,CAAC,CACfI,QAAQ,CAAC,IAAI,CAAC,CACdE,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAA2B,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI1B,aAAa,CAAC2B,OAAO,CAAE,CACzB3B,aAAa,CAAC2B,OAAO,CAACC,aAAa,CAAC,CAAC,CACvC,CACF,CAAC,CAED,mBACEtC,KAAA,QAAKuC,KAAK,CAAE,CACVC,OAAO,CAAE,MAAM,CACfC,MAAM,CAAE,OAAO,CACfC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,mEACd,CAAE,CAAAC,QAAA,eAEA/C,IAAA,QAAKyC,KAAK,CAAE,CACVO,IAAI,CAAE,CAAC,CACPN,OAAO,CAAE,MAAM,CACfO,aAAa,CAAE,QAAQ,CACvBC,QAAQ,CAAE,CACZ,CAAE,CAAAH,QAAA,cAEA7C,KAAA,QAAKuC,KAAK,CAAE,CACVO,IAAI,CAAE,CAAC,CACPG,QAAQ,CAAE,QAAQ,CAClBT,OAAO,CAAE,MAAM,CACfO,aAAa,CAAE,QACjB,CAAE,CAAAF,QAAA,EACCvC,KAAK,eACJR,IAAA,QAAKyC,KAAK,CAAE,CACVW,OAAO,CAAE,MAAM,CACfR,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,MAAM,CACbQ,MAAM,CAAE,KAAK,CACbC,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,MACZ,CAAE,CAAAR,QAAA,CACCvC,KAAK,CACH,CACN,cAEDR,IAAA,CAACH,UAAU,EACTO,MAAM,CAAEA,MAAO,CACfM,aAAa,CAAEA,aAAc,CAC7BJ,SAAS,CAAEA,SAAU,CACtB,CAAC,EACC,CAAC,CACH,CAAC,cAGNJ,KAAA,QAAKuC,KAAK,CAAE,CACVe,KAAK,CAAE,OAAO,CACdZ,eAAe,CAAE,SAAS,CAC1Ba,UAAU,CAAE,gBAAgB,CAC5Bf,OAAO,CAAE,MAAM,CACfO,aAAa,CAAE,QACjB,CAAE,CAAAF,QAAA,eAEA7C,KAAA,QAAKuC,KAAK,CAAE,CACVW,OAAO,CAAE,WAAW,CACpBM,YAAY,CAAE,gBAAgB,CAC9Bd,eAAe,CAAE,MAAM,CACvBF,OAAO,CAAE,MAAM,CACfiB,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAClB,CAAE,CAAAb,QAAA,eACA/C,IAAA,QAAKyC,KAAK,CAAE,CACVc,QAAQ,CAAE,MAAM,CAChBM,UAAU,CAAE,KAAK,CACjBhB,KAAK,CAAE,MACT,CAAE,CAAAE,QAAA,CAAC,sCAEH,CAAK,CAAC,cAEN7C,KAAA,QAAKuC,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEoB,GAAG,CAAE,KAAM,CAAE,CAAAf,QAAA,eAC1C/C,IAAA,WACE+D,OAAO,CAAE1B,WAAY,CACrBI,KAAK,CAAE,CACLW,OAAO,CAAE,UAAU,CACnBR,eAAe,CAAE,MAAM,CACvBC,KAAK,CAAE,MAAM,CACbmB,MAAM,CAAE,MAAM,CACdV,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,MAAM,CAChBU,MAAM,CAAE,SACV,CAAE,CAAAlB,QAAA,CACH,cAED,CAAQ,CAAC,cACT7C,KAAA,WACE6D,OAAO,CAAEzB,WAAY,CACrB4B,QAAQ,CAAE5D,SAAU,CACpBmC,KAAK,CAAE,CACLW,OAAO,CAAE,UAAU,CACnBR,eAAe,CAAEtC,SAAS,CAAG,MAAM,CAAG,SAAS,CAC/CuC,KAAK,CAAE,MAAM,CACbmB,MAAM,CAAE,MAAM,CACdV,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,MAAM,CAChBU,MAAM,CAAE3D,SAAS,CAAG,aAAa,CAAG,SAAS,CAC7C6D,QAAQ,CAAE,UAAU,CACpBhB,QAAQ,CAAE,QACZ,CAAE,CAAAJ,QAAA,EAEDzC,SAAS,eACRN,IAAA,QAAKyC,KAAK,CAAE,CACV0B,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,yEAAyE,CACrFC,SAAS,CAAE,uBACb,CAAE,CAAE,CACL,CACAnE,SAAS,CAAG,QAAQ,CAAG,IAAI,EACtB,CAAC,EACN,CAAC,EACH,CAAC,cAGNN,IAAA,QAAKyC,KAAK,CAAE,CACVO,IAAI,CAAE,CAAC,CACPG,QAAQ,CAAE,MAAM,CAChBC,OAAO,CAAE,MACX,CAAE,CAAAL,QAAA,cACA/C,IAAA,CAACJ,UAAU,EACT8E,GAAG,CAAE9D,aAAc,CACnB+D,QAAQ,CAAE9D,YAAa,CACvBP,SAAS,CAAEA,SAAU,CACtB,CAAC,CACC,CAAC,EACH,CAAC,cAGNN,IAAA,UAAA+C,QAAA,CAAQ;AACd;AACA;AACA;AACA;AACA,OAAO,CAAQ,CAAC,EACP,CAAC,CAEV,CAEA,cAAe,CAAA5C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}