{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import{getApiUrl}from'./config/api';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ResultView=_ref=>{let{result,originalImage}=_ref;const[imageLoaded,setImageLoaded]=useState(false);const[imageError,setImageError]=useState(false);const[apiBaseUrl,setApiBaseUrl]=useState('');const[showParams,setShowParams]=useState(false);const[splitPosition,setSplitPosition]=useState(50);// 分割线位置百分比\nconst[isDragging,setIsDragging]=useState(false);const[viewMode,setViewMode]=useState('split');// 'split', 'side-by-side'\nconst[zoomLevel,setZoomLevel]=useState(1);// 缩放级别\nconst[panOffset,setPanOffset]=useState({x:0,y:0});// 平移偏移\nconst[isPanning,setIsPanning]=useState(false);const[lastPanPoint,setLastPanPoint]=useState({x:0,y:0});const containerRef=useRef(null);const enhancedImageRef=useRef(null);const imageContainerRef=useRef(null);// 获取API基础URL\nuseEffect(()=>{const loadApiUrl=async()=>{try{const baseUrl=await getApiUrl('');setApiBaseUrl(baseUrl);}catch(error){console.error('获取API地址失败:',error);}};loadApiUrl();},[]);const handleImageLoad=()=>{setImageLoaded(true);setImageError(false);};const handleImageError=()=>{setImageError(true);setImageLoaded(false);};const downloadImage=()=>{const link=document.createElement('a');link.href=`${apiBaseUrl}/result/${result.enhanced_url}`;link.download=`enhanced_${result.filename}`;document.body.appendChild(link);link.click();document.body.removeChild(link);};// 分割线拖拽处理\nconst handleSplitMouseDown=e=>{setIsDragging(true);e.preventDefault();e.stopPropagation();// 阻止事件冒泡，避免触发图像平移\n};const handleSplitMouseMove=e=>{if(!isDragging||!imageContainerRef.current)return;const rect=imageContainerRef.current.getBoundingClientRect();const x=e.clientX-rect.left;const percentage=Math.max(0,Math.min(100,x/rect.width*100));setSplitPosition(percentage);};const handleSplitMouseUp=()=>{setIsDragging(false);};// 缩放处理\nconst handleWheel=e=>{if(!imageContainerRef.current)return;e.preventDefault();const delta=e.deltaY>0?-0.1:0.1;const newZoom=Math.max(0.5,Math.min(5,zoomLevel+delta));setZoomLevel(newZoom);};// 平移处理\nconst handlePanStart=e=>{if(zoomLevel<=1||isDragging)return;// 只有放大时才允许平移，且不在拖拽分割线时\nsetIsPanning(true);setLastPanPoint({x:e.clientX,y:e.clientY});e.preventDefault();};// 图像容器的鼠标按下处理（避免与分割线拖拽冲突）\nconst handleImageMouseDown=e=>{// 检查是否点击在分割线上（通过检查目标元素）\nif(e.target.closest('[data-split-line]')){return;// 如果点击在分割线上，不处理平移\n}if(zoomLevel>1&&!isDragging){handlePanStart(e);}};const handlePanMove=e=>{if(!isPanning)return;const deltaX=e.clientX-lastPanPoint.x;const deltaY=e.clientY-lastPanPoint.y;setPanOffset(prev=>({x:prev.x+deltaX,y:prev.y+deltaY}));setLastPanPoint({x:e.clientX,y:e.clientY});};const handlePanEnd=()=>{setIsPanning(false);};// 重置缩放和平移\nconst resetZoom=()=>{setZoomLevel(1);setPanOffset({x:0,y:0});};// 缩放控制函数\nconst zoomIn=()=>{setZoomLevel(prev=>Math.min(5,prev+0.25));};const zoomOut=()=>{setZoomLevel(prev=>Math.max(0.5,prev-0.25));};// 添加全局鼠标事件监听\nuseEffect(()=>{if(isDragging){document.addEventListener('mousemove',handleSplitMouseMove);document.addEventListener('mouseup',handleSplitMouseUp);return()=>{document.removeEventListener('mousemove',handleSplitMouseMove);document.removeEventListener('mouseup',handleSplitMouseUp);};}},[isDragging]);// 添加平移事件监听\nuseEffect(()=>{if(isPanning){document.addEventListener('mousemove',handlePanMove);document.addEventListener('mouseup',handlePanEnd);return()=>{document.removeEventListener('mousemove',handlePanMove);document.removeEventListener('mouseup',handlePanEnd);};}},[isPanning,lastPanPoint]);// 重置分割线位置\nconst resetSplit=()=>{setSplitPosition(50);};return/*#__PURE__*/_jsxs(\"div\",{style:{width:'100%',height:'100%',display:'flex',flexDirection:'column',backgroundColor:'#1e1e1e',color:'#ddd'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{height:'40px',backgroundColor:'#2b2b2b',borderBottom:'1px solid #555',display:'flex',alignItems:'center',justifyContent:'space-between',padding:'0 16px',flexShrink:0},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'12px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'8px',height:'8px',borderRadius:'50%',backgroundColor:'#28ca42'}}),/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'13px',color:'#ddd'},children:\"\\u5904\\u7406\\u5B8C\\u6210\"}),result.message&&/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'12px',color:'#aaa'},children:[\"\\u2022 \",result.message]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'6px',alignItems:'center'},children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('split'),style:{padding:'4px 8px',backgroundColor:viewMode==='split'?'#4a90e2':'#555',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'12px',transition:'background-color 0.2s'},children:\"\\u5206\\u5272\\u5BF9\\u6BD4\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setViewMode('side-by-side'),style:{padding:'4px 8px',backgroundColor:viewMode==='side-by-side'?'#4a90e2':'#555',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'12px',transition:'background-color 0.2s'},children:\"\\u5E76\\u6392\\u5BF9\\u6BD4\"}),viewMode==='split'&&/*#__PURE__*/_jsx(\"button\",{onClick:resetSplit,style:{padding:'4px 8px',backgroundColor:'#28a745',color:'white',border:'none',borderRadius:'3px',cursor:'pointer',fontSize:'12px',transition:'background-color 0.2s'},children:\"\\u91CD\\u7F6E\"})]})]}),result.params_used&&/*#__PURE__*/_jsxs(\"div\",{style:{height:showParams?'auto':'32px',backgroundColor:'#2b2b2b',borderBottom:'1px solid #555',padding:'8px 16px',flexShrink:0,transition:'height 0.3s ease'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'space-between'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'16px'},children:[/*#__PURE__*/_jsx(\"span\",{style:{fontSize:'12px',color:'#aaa'},children:\"\\u5904\\u7406\\u53C2\\u6570:\"}),!showParams&&/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',gap:'12px',fontSize:'12px',color:'#ddd'},children:[/*#__PURE__*/_jsxs(\"span\",{children:[result.params_used.scale,\"x\\u8D85\\u5206\"]}),/*#__PURE__*/_jsx(\"span\",{children:result.params_used.use_realesrgan?'RealESRGAN':'插值'}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u9510\\u5316\",(result.params_used.sharpening*100).toFixed(0),\"%\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u7F8E\\u989C\",(result.params_used.beauty*100).toFixed(0),\"%\"]})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowParams(!showParams),style:{padding:'2px 6px',backgroundColor:'transparent',color:'#aaa',border:'1px solid #555',borderRadius:'3px',cursor:'pointer',fontSize:'11px',transition:'all 0.2s'},onMouseEnter:e=>{e.target.style.backgroundColor='#555';e.target.style.color='#fff';},onMouseLeave:e=>{e.target.style.backgroundColor='transparent';e.target.style.color='#aaa';},children:showParams?'▲':'▼'})]}),showParams&&/*#__PURE__*/_jsxs(\"div\",{style:{marginTop:'12px',display:'grid',gridTemplateColumns:'repeat(auto-fit, minmax(120px, 1fr))',gap:'8px',fontSize:'12px',color:'#ddd'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u8D85\\u5206\\u500D\\u6570:\"}),\" \",result.params_used.scale,\"x\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"AI\\u6A21\\u578B:\"}),\" \",result.params_used.use_realesrgan?'RealESRGAN':'简单插值']}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u9510\\u5316:\"}),\" \",(result.params_used.sharpening*100).toFixed(0),\"%\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u964D\\u566A:\"}),\" \",result.params_used.denoising]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u9971\\u548C\\u5EA6:\"}),\" \",result.params_used.saturation.toFixed(1)]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u5BF9\\u6BD4\\u5EA6:\"}),\" \",result.params_used.contrast.toFixed(1)]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u4EAE\\u5EA6:\"}),\" \",result.params_used.brightness>0?'+':'',result.params_used.brightness]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"span\",{style:{color:'#aaa'},children:\"\\u7F8E\\u989C:\"}),\" \",(result.params_used.beauty*100).toFixed(0),\"%\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{flex:1,display:'flex',flexDirection:'column',overflow:'hidden',position:'relative'},children:viewMode==='split'?/*#__PURE__*/// 分割线对比模式\n_jsxs(\"div\",{style:{flex:1,display:'flex',flexDirection:'column',padding:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{height:'32px',display:'flex',alignItems:'center',justifyContent:'space-between',marginBottom:'12px',fontSize:'12px',color:'#aaa'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',gap:'8px'},children:/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5DE6\\u4FA7\\uFF1A\\u539F\\u59CB\\u56FE\\u50CF | \\u53F3\\u4FA7\\uFF1A\\u589E\\u5F3A\\u56FE\\u50CF | \\u5206\\u5272\\u4F4D\\u7F6E\\uFF1A\",splitPosition.toFixed(0),\"%\"]})}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'4px',position:'relative',zIndex:1000// 确保按钮始终在最上层\n},children:[/*#__PURE__*/_jsx(\"button\",{onClick:zoomOut,disabled:zoomLevel<=0.5,style:{width:'24px',height:'24px',backgroundColor:zoomLevel<=0.5?'#444':'#555',color:zoomLevel<=0.5?'#666':'#fff',border:'1px solid #666',borderRadius:'3px',cursor:zoomLevel<=0.5?'not-allowed':'pointer',fontSize:'12px',display:'flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:1001},title:\"\\u7F29\\u5C0F\",children:\"\\u2212\"}),/*#__PURE__*/_jsxs(\"span\",{style:{minWidth:'40px',textAlign:'center',fontSize:'11px',color:zoomLevel===1?'#aaa':'#4a90e2'},children:[(zoomLevel*100).toFixed(0),\"%\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:zoomIn,disabled:zoomLevel>=5,style:{width:'24px',height:'24px',backgroundColor:zoomLevel>=5?'#444':'#555',color:zoomLevel>=5?'#666':'#fff',border:'1px solid #666',borderRadius:'3px',cursor:zoomLevel>=5?'not-allowed':'pointer',fontSize:'12px',display:'flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:1001},title:\"\\u653E\\u5927\",children:\"+\"}),/*#__PURE__*/_jsx(\"button\",{onClick:resetZoom,disabled:zoomLevel===1&&panOffset.x===0&&panOffset.y===0,style:{width:'24px',height:'24px',backgroundColor:zoomLevel===1&&panOffset.x===0&&panOffset.y===0?'#444':'#555',color:zoomLevel===1&&panOffset.x===0&&panOffset.y===0?'#666':'#fff',border:'1px solid #666',borderRadius:'3px',cursor:zoomLevel===1&&panOffset.x===0&&panOffset.y===0?'not-allowed':'pointer',fontSize:'10px',display:'flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:1001},title:\"\\u91CD\\u7F6E\\u7F29\\u653E\",children:\"\\u2302\"})]})]}),/*#__PURE__*/_jsx(\"div\",{ref:containerRef,style:{position:'relative',width:'100%',height:'100%',cursor:isDragging?'ew-resize':'default',userSelect:'none',display:'flex',alignItems:'center',justifyContent:'center',overflow:'auto'// 添加滚动条支持\n},children:originalImage&&/*#__PURE__*/_jsx(\"div\",{style:{position:'relative',width:'100%',height:'100%',maxHeight:'calc(100vh - 200px)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsxs(\"div\",{ref:imageContainerRef,style:{position:'relative',display:'inline-block',border:'1px solid #555',borderRadius:'4px',overflow:'hidden',transform:`scale(${zoomLevel}) translate(${panOffset.x/zoomLevel}px, ${panOffset.y/zoomLevel}px)`,transformOrigin:'center center',transition:isPanning?'none':'transform 0.2s ease-out',cursor:zoomLevel>1?isPanning?'grabbing':'grab':'default'},onWheel:handleWheel,onMouseDown:handleImageMouseDown,children:[imageError?/*#__PURE__*/_jsx(\"div\",{style:{width:'400px',height:'300px',display:'flex',alignItems:'center',justifyContent:'center',color:'#ff6b6b',backgroundColor:'#2b2b2b'},children:\"\\u589E\\u5F3A\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"}):/*#__PURE__*/_jsx(\"img\",{ref:enhancedImageRef,src:`${apiBaseUrl}/result/${result.enhanced_url}?t=${Date.now()}`,alt:\"\\u589E\\u5F3A\\u56FE\\u50CF\",onLoad:handleImageLoad,onError:handleImageError,style:{maxWidth:'100%',maxHeight:'calc(100vh - 250px)',height:'auto',display:'block',opacity:imageLoaded?1:0.5,imageRendering:'auto'// 增强图像使用高质量渲染\n}}),imageLoaded&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,width:`${splitPosition}%`,height:'100%',overflow:'hidden'},children:/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"\\u539F\\u59CB\\u56FE\\u50CF\",style:{// 确保原图与增强图片尺寸完全匹配\nwidth:`${100*100/splitPosition}%`,height:'100%',objectFit:'fill',// 强制填充，实现最近邻插值效果\nimageRendering:'pixelated',// 最近邻插值，保持像素清晰\nposition:'absolute',left:0,top:0}})}),imageLoaded&&/*#__PURE__*/_jsx(\"div\",{\"data-split-line\":\"true\",style:{position:'absolute',top:0,left:`${splitPosition}%`,width:'2px',height:'100%',backgroundColor:'#4a90e2',cursor:'ew-resize',boxShadow:'0 0 4px rgba(74, 144, 226, 0.5)',transform:'translateX(-1px)',zIndex:10},onMouseDown:handleSplitMouseDown,children:/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',width:'16px',height:'32px',backgroundColor:'#4a90e2',borderRadius:'8px',display:'flex',alignItems:'center',justifyContent:'center',color:'white',fontSize:'10px',fontWeight:'bold',boxShadow:'0 2px 4px rgba(0,0,0,0.3)'},children:\"\\u27F7\"})}),!imageLoaded&&!imageError&&/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'50%',left:'50%',transform:'translate(-50%, -50%)',color:'#aaa',backgroundColor:'rgba(43,43,43,0.9)',padding:'8px 12px',borderRadius:'4px',fontSize:'12px'},children:\"\\u52A0\\u8F7D\\u4E2D...\"})]})})})]}):/*#__PURE__*/// 并排对比模式\n_jsxs(\"div\",{style:{flex:1,display:'flex',flexDirection:'column',padding:'16px'},children:[/*#__PURE__*/_jsxs(\"div\",{style:{height:'32px',display:'flex',alignItems:'center',justifyContent:'space-between',marginBottom:'12px',fontSize:'12px',color:'#aaa'},children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u5E76\\u6392\\u5BF9\\u6BD4\\u6A21\\u5F0F\"}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'4px',position:'relative',zIndex:1000// 确保按钮始终在最上层\n},children:[/*#__PURE__*/_jsx(\"button\",{onClick:zoomOut,disabled:zoomLevel<=0.5,style:{width:'24px',height:'24px',backgroundColor:zoomLevel<=0.5?'#444':'#555',color:zoomLevel<=0.5?'#666':'#fff',border:'1px solid #666',borderRadius:'3px',cursor:zoomLevel<=0.5?'not-allowed':'pointer',fontSize:'12px',display:'flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:1001},title:\"\\u7F29\\u5C0F\",children:\"\\u2212\"}),/*#__PURE__*/_jsxs(\"span\",{style:{minWidth:'40px',textAlign:'center',fontSize:'11px',color:zoomLevel===1?'#aaa':'#4a90e2'},children:[(zoomLevel*100).toFixed(0),\"%\"]}),/*#__PURE__*/_jsx(\"button\",{onClick:zoomIn,disabled:zoomLevel>=5,style:{width:'24px',height:'24px',backgroundColor:zoomLevel>=5?'#444':'#555',color:zoomLevel>=5?'#666':'#fff',border:'1px solid #666',borderRadius:'3px',cursor:zoomLevel>=5?'not-allowed':'pointer',fontSize:'12px',display:'flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:1001},title:\"\\u653E\\u5927\",children:\"+\"}),/*#__PURE__*/_jsx(\"button\",{onClick:resetZoom,disabled:zoomLevel===1&&panOffset.x===0&&panOffset.y===0,style:{width:'24px',height:'24px',backgroundColor:zoomLevel===1&&panOffset.x===0&&panOffset.y===0?'#444':'#555',color:zoomLevel===1&&panOffset.x===0&&panOffset.y===0?'#666':'#fff',border:'1px solid #666',borderRadius:'3px',cursor:zoomLevel===1&&panOffset.x===0&&panOffset.y===0?'not-allowed':'pointer',fontSize:'10px',display:'flex',alignItems:'center',justifyContent:'center',position:'relative',zIndex:1001},title:\"\\u91CD\\u7F6E\\u7F29\\u653E\",children:\"\\u2302\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1,display:'flex',gap:'16px',alignItems:'center',justifyContent:'center',overflow:'auto'// 添加滚动条支持\n},children:[originalImage&&/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',textAlign:'center',maxWidth:'50%'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'8px',fontSize:'12px',color:'#aaa'},children:\"\\u539F\\u59CB\\u56FE\\u50CF\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:'inline-block',transform:`scale(${zoomLevel}) translate(${panOffset.x/zoomLevel}px, ${panOffset.y/zoomLevel}px)`,transformOrigin:'center center',transition:isPanning?'none':'transform 0.2s ease-out',cursor:zoomLevel>1?isPanning?'grabbing':'grab':'default'},onWheel:handleWheel,onMouseDown:handleImageMouseDown,children:/*#__PURE__*/_jsx(\"img\",{src:originalImage,alt:\"\\u539F\\u59CB\\u56FE\\u50CF\",style:{maxWidth:'100%',maxHeight:'calc(100vh - 300px)',height:'auto',border:'1px solid #4a90e2',borderRadius:'4px',objectFit:'contain',imageRendering:'pixelated'// 最近邻插值，保持像素清晰\n}})})]}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:'1',textAlign:'center',maxWidth:'50%'},children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'8px',fontSize:'12px',color:'#aaa'},children:\"\\u589E\\u5F3A\\u56FE\\u50CF\"}),!imageLoaded&&!imageError&&/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'200px',display:'flex',alignItems:'center',justifyContent:'center',border:'1px dashed #666',borderRadius:'4px',color:'#aaa',backgroundColor:'#2b2b2b'},children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u56FE\\u50CF...\"}),imageError&&/*#__PURE__*/_jsx(\"div\",{style:{width:'100%',height:'200px',display:'flex',alignItems:'center',justifyContent:'center',border:'1px solid #ff6b6b',borderRadius:'4px',color:'#ff6b6b',backgroundColor:'#2b2b2b'},children:\"\\u56FE\\u50CF\\u52A0\\u8F7D\\u5931\\u8D25\"}),/*#__PURE__*/_jsx(\"div\",{style:{display:imageLoaded?'inline-block':'none',transform:`scale(${zoomLevel}) translate(${panOffset.x/zoomLevel}px, ${panOffset.y/zoomLevel}px)`,transformOrigin:'center center',transition:isPanning?'none':'transform 0.2s ease-out',cursor:zoomLevel>1?isPanning?'grabbing':'grab':'default'},onWheel:handleWheel,onMouseDown:handleImageMouseDown,children:/*#__PURE__*/_jsx(\"img\",{src:`${apiBaseUrl}/result/${result.enhanced_url}?t=${Date.now()}`,alt:\"\\u589E\\u5F3A\\u56FE\\u50CF\",onLoad:handleImageLoad,onError:handleImageError,style:{maxWidth:'100%',maxHeight:'calc(100vh - 300px)',height:'auto',border:'1px solid #28ca42',borderRadius:'4px',objectFit:'contain'}})})]})]})]})}),imageLoaded&&/*#__PURE__*/_jsxs(\"div\",{style:{height:'48px',backgroundColor:'#333',borderTop:'1px solid #555',display:'flex',alignItems:'center',justifyContent:'center',gap:'12px',padding:'0 16px',flexShrink:0},children:[/*#__PURE__*/_jsxs(\"button\",{onClick:downloadImage,style:{padding:'6px 12px',backgroundColor:'#28ca42',color:'white',border:'none',borderRadius:'4px',cursor:'pointer',fontSize:'13px',display:'flex',alignItems:'center',gap:'6px',transition:'background-color 0.2s'},onMouseEnter:e=>e.target.style.backgroundColor='#22a83a',onMouseLeave:e=>e.target.style.backgroundColor='#28ca42',children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCE5\"}),\"\\u4E0B\\u8F7D\\u56FE\\u50CF\"]}),/*#__PURE__*/_jsxs(\"a\",{href:`${apiBaseUrl}/result/${result.enhanced_url}`,target:\"_blank\",rel:\"noopener noreferrer\",style:{padding:'6px 12px',backgroundColor:'#4a90e2',color:'white',textDecoration:'none',borderRadius:'4px',fontSize:'13px',display:'flex',alignItems:'center',gap:'6px',transition:'background-color 0.2s'},onMouseEnter:e=>e.target.style.backgroundColor='#357abd',onMouseLeave:e=>e.target.style.backgroundColor='#4a90e2',children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDD0D\"}),\"\\u65B0\\u7A97\\u53E3\\u67E5\\u770B\"]}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px',color:'#aaa',marginLeft:'auto'},children:[\"\\u6587\\u4EF6: \",result.filename]})]})]});};export default ResultView;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "getApiUrl", "jsx", "_jsx", "jsxs", "_jsxs", "ResultView", "_ref", "result", "originalImage", "imageLoaded", "setImageLoaded", "imageError", "setImageError", "apiBaseUrl", "setApiBaseUrl", "showParams", "setShowParams", "splitPosition", "setSplitPosition", "isDragging", "setIsDragging", "viewMode", "setViewMode", "zoomLevel", "setZoomLevel", "panOffset", "setPanOffset", "x", "y", "isPanning", "setIsPanning", "lastPanPoint", "setLastPanPoint", "containerRef", "enhancedImageRef", "imageContainerRef", "loadApiUrl", "baseUrl", "error", "console", "handleImageLoad", "handleImageError", "downloadImage", "link", "document", "createElement", "href", "enhanced_url", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "handleSplitMouseDown", "e", "preventDefault", "stopPropagation", "handleSplitMouseMove", "current", "rect", "getBoundingClientRect", "clientX", "left", "percentage", "Math", "max", "min", "width", "handleSplitMouseUp", "handleWheel", "delta", "deltaY", "newZoom", "handlePanStart", "clientY", "handleImageMouseDown", "target", "closest", "handlePanMove", "deltaX", "prev", "handlePanEnd", "resetZoom", "zoomIn", "zoomOut", "addEventListener", "removeEventListener", "resetSplit", "style", "height", "display", "flexDirection", "backgroundColor", "color", "children", "borderBottom", "alignItems", "justifyContent", "padding", "flexShrink", "gap", "borderRadius", "fontSize", "message", "onClick", "border", "cursor", "transition", "params_used", "scale", "use_realesrgan", "sharpening", "toFixed", "beauty", "onMouseEnter", "onMouseLeave", "marginTop", "gridTemplateColumns", "denoising", "saturation", "contrast", "brightness", "flex", "overflow", "position", "marginBottom", "zIndex", "disabled", "title", "min<PERSON><PERSON><PERSON>", "textAlign", "ref", "userSelect", "maxHeight", "transform", "transform<PERSON><PERSON>in", "onWheel", "onMouseDown", "src", "Date", "now", "alt", "onLoad", "onError", "max<PERSON><PERSON><PERSON>", "opacity", "imageRendering", "top", "objectFit", "boxShadow", "fontWeight", "borderTop", "rel", "textDecoration", "marginLeft"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/ResultView.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { getApiUrl } from './config/api';\n\nconst ResultView = ({ result, originalImage }) => {\n  const [imageLoaded, setImageLoaded] = useState(false);\n  const [imageError, setImageError] = useState(false);\n  const [apiBaseUrl, setApiBaseUrl] = useState('');\n  const [showParams, setShowParams] = useState(false);\n  const [splitPosition, setSplitPosition] = useState(50); // 分割线位置百分比\n  const [isDragging, setIsDragging] = useState(false);\n  const [viewMode, setViewMode] = useState('split'); // 'split', 'side-by-side'\n  const [zoomLevel, setZoomLevel] = useState(1); // 缩放级别\n  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 }); // 平移偏移\n  const [isPanning, setIsPanning] = useState(false);\n  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });\n  const containerRef = useRef(null);\n  const enhancedImageRef = useRef(null);\n  const imageContainerRef = useRef(null);\n\n  // 获取API基础URL\n  useEffect(() => {\n    const loadApiUrl = async () => {\n      try {\n        const baseUrl = await getApiUrl('');\n        setApiBaseUrl(baseUrl);\n      } catch (error) {\n        console.error('获取API地址失败:', error);\n      }\n    };\n    loadApiUrl();\n  }, []);\n\n  const handleImageLoad = () => {\n    setImageLoaded(true);\n    setImageError(false);\n  };\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoaded(false);\n  };\n\n  const downloadImage = () => {\n    const link = document.createElement('a');\n    link.href = `${apiBaseUrl}/result/${result.enhanced_url}`;\n    link.download = `enhanced_${result.filename}`;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  };\n\n  // 分割线拖拽处理\n  const handleSplitMouseDown = (e) => {\n    setIsDragging(true);\n    e.preventDefault();\n    e.stopPropagation(); // 阻止事件冒泡，避免触发图像平移\n  };\n\n  const handleSplitMouseMove = (e) => {\n    if (!isDragging || !imageContainerRef.current) return;\n\n    const rect = imageContainerRef.current.getBoundingClientRect();\n    const x = e.clientX - rect.left;\n    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));\n    setSplitPosition(percentage);\n  };\n\n  const handleSplitMouseUp = () => {\n    setIsDragging(false);\n  };\n\n  // 缩放处理\n  const handleWheel = (e) => {\n    if (!imageContainerRef.current) return;\n\n    e.preventDefault();\n    const delta = e.deltaY > 0 ? -0.1 : 0.1;\n    const newZoom = Math.max(0.5, Math.min(5, zoomLevel + delta));\n    setZoomLevel(newZoom);\n  };\n\n  // 平移处理\n  const handlePanStart = (e) => {\n    if (zoomLevel <= 1 || isDragging) return; // 只有放大时才允许平移，且不在拖拽分割线时\n\n    setIsPanning(true);\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n    e.preventDefault();\n  };\n\n  // 图像容器的鼠标按下处理（避免与分割线拖拽冲突）\n  const handleImageMouseDown = (e) => {\n    // 检查是否点击在分割线上（通过检查目标元素）\n    if (e.target.closest('[data-split-line]')) {\n      return; // 如果点击在分割线上，不处理平移\n    }\n\n    if (zoomLevel > 1 && !isDragging) {\n      handlePanStart(e);\n    }\n  };\n\n  const handlePanMove = (e) => {\n    if (!isPanning) return;\n\n    const deltaX = e.clientX - lastPanPoint.x;\n    const deltaY = e.clientY - lastPanPoint.y;\n\n    setPanOffset(prev => ({\n      x: prev.x + deltaX,\n      y: prev.y + deltaY\n    }));\n\n    setLastPanPoint({\n      x: e.clientX,\n      y: e.clientY\n    });\n  };\n\n  const handlePanEnd = () => {\n    setIsPanning(false);\n  };\n\n  // 重置缩放和平移\n  const resetZoom = () => {\n    setZoomLevel(1);\n    setPanOffset({ x: 0, y: 0 });\n  };\n\n  // 缩放控制函数\n  const zoomIn = () => {\n    setZoomLevel(prev => Math.min(5, prev + 0.25));\n  };\n\n  const zoomOut = () => {\n    setZoomLevel(prev => Math.max(0.5, prev - 0.25));\n  };\n\n  // 添加全局鼠标事件监听\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleSplitMouseMove);\n      document.addEventListener('mouseup', handleSplitMouseUp);\n      return () => {\n        document.removeEventListener('mousemove', handleSplitMouseMove);\n        document.removeEventListener('mouseup', handleSplitMouseUp);\n      };\n    }\n  }, [isDragging]);\n\n  // 添加平移事件监听\n  useEffect(() => {\n    if (isPanning) {\n      document.addEventListener('mousemove', handlePanMove);\n      document.addEventListener('mouseup', handlePanEnd);\n      return () => {\n        document.removeEventListener('mousemove', handlePanMove);\n        document.removeEventListener('mouseup', handlePanEnd);\n      };\n    }\n  }, [isPanning, lastPanPoint]);\n\n\n\n  // 重置分割线位置\n  const resetSplit = () => {\n    setSplitPosition(50);\n  };\n\n  return (\n    <div style={{ \n      width: '100%', \n      height: '100%', \n      display: 'flex', \n      flexDirection: 'column',\n      backgroundColor: '#1e1e1e',\n      color: '#ddd'\n    }}>\n      {/* 顶部工具栏 */}\n      <div style={{\n        height: '40px',\n        backgroundColor: '#2b2b2b',\n        borderBottom: '1px solid #555',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        padding: '0 16px',\n        flexShrink: 0\n      }}>\n        {/* 左侧状态指示 */}\n        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>\n          <div style={{\n            width: '8px',\n            height: '8px',\n            borderRadius: '50%',\n            backgroundColor: '#28ca42'\n          }}></div>\n          <span style={{ fontSize: '13px', color: '#ddd' }}>处理完成</span>\n          {result.message && (\n            <span style={{ fontSize: '12px', color: '#aaa' }}>• {result.message}</span>\n          )}\n        </div>\n\n        {/* 右侧视图控制 */}\n        <div style={{ display: 'flex', gap: '6px', alignItems: 'center' }}>\n          <button\n            onClick={() => setViewMode('split')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'split' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            分割对比\n          </button>\n          <button\n            onClick={() => setViewMode('side-by-side')}\n            style={{\n              padding: '4px 8px',\n              backgroundColor: viewMode === 'side-by-side' ? '#4a90e2' : '#555',\n              color: 'white',\n              border: 'none',\n              borderRadius: '3px',\n              cursor: 'pointer',\n              fontSize: '12px',\n              transition: 'background-color 0.2s'\n            }}\n          >\n            并排对比\n          </button>\n          {viewMode === 'split' && (\n            <button\n              onClick={resetSplit}\n              style={{\n                padding: '4px 8px',\n                backgroundColor: '#28a745',\n                color: 'white',\n                border: 'none',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '12px',\n                transition: 'background-color 0.2s'\n              }}\n            >\n              重置\n            </button>\n          )}\n        </div>\n      </div>\n\n      {/* 参数信息栏 */}\n      {result.params_used && (\n        <div style={{\n          height: showParams ? 'auto' : '32px',\n          backgroundColor: '#2b2b2b',\n          borderBottom: '1px solid #555',\n          padding: '8px 16px',\n          flexShrink: 0,\n          transition: 'height 0.3s ease'\n        }}>\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>\n              <span style={{ fontSize: '12px', color: '#aaa' }}>处理参数:</span>\n              {!showParams && (\n                <div style={{ display: 'flex', gap: '12px', fontSize: '12px', color: '#ddd' }}>\n                  <span>{result.params_used.scale}x超分</span>\n                  <span>{result.params_used.use_realesrgan ? 'RealESRGAN' : '插值'}</span>\n                  <span>锐化{(result.params_used.sharpening * 100).toFixed(0)}%</span>\n                  <span>美颜{(result.params_used.beauty * 100).toFixed(0)}%</span>\n                </div>\n              )}\n            </div>\n            <button\n              onClick={() => setShowParams(!showParams)}\n              style={{\n                padding: '2px 6px',\n                backgroundColor: 'transparent',\n                color: '#aaa',\n                border: '1px solid #555',\n                borderRadius: '3px',\n                cursor: 'pointer',\n                fontSize: '11px',\n                transition: 'all 0.2s'\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.backgroundColor = '#555';\n                e.target.style.color = '#fff';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.backgroundColor = 'transparent';\n                e.target.style.color = '#aaa';\n              }}\n            >\n              {showParams ? '▲' : '▼'}\n            </button>\n          </div>\n\n          {showParams && (\n            <div style={{ \n              marginTop: '12px', \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', \n              gap: '8px',\n              fontSize: '12px',\n              color: '#ddd'\n            }}>\n              <div><span style={{ color: '#aaa' }}>超分倍数:</span> {result.params_used.scale}x</div>\n              <div><span style={{ color: '#aaa' }}>AI模型:</span> {result.params_used.use_realesrgan ? 'RealESRGAN' : '简单插值'}</div>\n              <div><span style={{ color: '#aaa' }}>锐化:</span> {(result.params_used.sharpening * 100).toFixed(0)}%</div>\n              <div><span style={{ color: '#aaa' }}>降噪:</span> {result.params_used.denoising}</div>\n              <div><span style={{ color: '#aaa' }}>饱和度:</span> {result.params_used.saturation.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>对比度:</span> {result.params_used.contrast.toFixed(1)}</div>\n              <div><span style={{ color: '#aaa' }}>亮度:</span> {result.params_used.brightness > 0 ? '+' : ''}{result.params_used.brightness}</div>\n              <div><span style={{ color: '#aaa' }}>美颜:</span> {(result.params_used.beauty * 100).toFixed(0)}%</div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 主图像显示区域 */}\n      <div style={{ \n        flex: 1, \n        display: 'flex', \n        flexDirection: 'column',\n        overflow: 'hidden',\n        position: 'relative'\n      }}>\n        {viewMode === 'split' ? (\n          // 分割线对比模式\n          <div style={{ \n            flex: 1, \n            display: 'flex', \n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 分割线信息栏和缩放控制 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <span>左侧：原始图像 | 右侧：增强图像 | 分割位置：{splitPosition.toFixed(0)}%</span>\n              </div>\n\n              {/* 缩放控制按钮 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                position: 'relative',\n                zIndex: 1000 // 确保按钮始终在最上层\n              }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            <div\n              ref={containerRef}\n              style={{\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                cursor: isDragging ? 'ew-resize' : 'default',\n                userSelect: 'none',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                overflow: 'auto' // 添加滚动条支持\n              }}\n            >\n              {originalImage && (\n                <div style={{ \n                  position: 'relative', \n                  width: '100%', \n                  height: '100%',\n                  maxHeight: 'calc(100vh - 200px)',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center'\n                }}>\n                  {/* 图像对比容器 - 支持缩放和平移 */}\n                  <div\n                    ref={imageContainerRef}\n                    style={{\n                      position: 'relative',\n                      display: 'inline-block',\n                      border: '1px solid #555',\n                      borderRadius: '4px',\n                      overflow: 'hidden',\n                      transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                      transformOrigin: 'center center',\n                      transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                      cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                    }}\n                    onWheel={handleWheel}\n                    onMouseDown={handleImageMouseDown}\n                  >\n                    {/* 增强图像作为背景 */}\n                    {imageError ? (\n                      <div style={{\n                        width: '400px',\n                        height: '300px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        color: '#ff6b6b',\n                        backgroundColor: '#2b2b2b'\n                      }}>\n                        增强图像加载失败\n                      </div>\n                    ) : (\n                      <img\n                        ref={enhancedImageRef}\n                        src={`${apiBaseUrl}/result/${result.enhanced_url}?t=${Date.now()}`}\n                        alt=\"增强图像\"\n                        onLoad={handleImageLoad}\n                        onError={handleImageError}\n                        style={{\n                          maxWidth: '100%',\n                          maxHeight: 'calc(100vh - 250px)',\n                          height: 'auto',\n                          display: 'block',\n                          opacity: imageLoaded ? 1 : 0.5,\n                          imageRendering: 'auto' // 增强图像使用高质量渲染\n                        }}\n                      />\n                    )}\n\n                    {/* 原始图像覆盖层 - 像素级对齐 */}\n                    {imageLoaded && (\n                      <div\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                          width: `${splitPosition}%`,\n                          height: '100%',\n                          overflow: 'hidden'\n                        }}\n                      >\n                        <img\n                          src={originalImage}\n                          alt=\"原始图像\"\n                          style={{\n                            // 确保原图与增强图片尺寸完全匹配\n                            width: `${100 * 100 / splitPosition}%`,\n                            height: '100%',\n                            objectFit: 'fill', // 强制填充，实现最近邻插值效果\n                            imageRendering: 'pixelated', // 最近邻插值，保持像素清晰\n                            position: 'absolute',\n                            left: 0,\n                            top: 0\n                          }}\n                        />\n                      </div>\n                    )}\n\n                    {/* 分割线 */}\n                    {imageLoaded && (\n                      <div\n                        data-split-line=\"true\"\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: `${splitPosition}%`,\n                          width: '2px',\n                          height: '100%',\n                          backgroundColor: '#4a90e2',\n                          cursor: 'ew-resize',\n                          boxShadow: '0 0 4px rgba(74, 144, 226, 0.5)',\n                          transform: 'translateX(-1px)',\n                          zIndex: 10\n                        }}\n                        onMouseDown={handleSplitMouseDown}\n                      >\n                        {/* 分割线手柄 */}\n                        <div\n                          style={{\n                            position: 'absolute',\n                            top: '50%',\n                            left: '50%',\n                            transform: 'translate(-50%, -50%)',\n                            width: '16px',\n                            height: '32px',\n                            backgroundColor: '#4a90e2',\n                            borderRadius: '8px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontSize: '10px',\n                            fontWeight: 'bold',\n                            boxShadow: '0 2px 4px rgba(0,0,0,0.3)'\n                          }}\n                        >\n                          ⟷\n                        </div>\n                      </div>\n                    )}\n\n                    {/* 加载状态 */}\n                    {!imageLoaded && !imageError && (\n                      <div style={{\n                        position: 'absolute',\n                        top: '50%',\n                        left: '50%',\n                        transform: 'translate(-50%, -50%)',\n                        color: '#aaa',\n                        backgroundColor: 'rgba(43,43,43,0.9)',\n                        padding: '8px 12px',\n                        borderRadius: '4px',\n                        fontSize: '12px'\n                      }}>\n                        加载中...\n                      </div>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        ) : (\n          // 并排对比模式\n          <div style={{\n            flex: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            padding: '16px'\n          }}>\n            {/* 并排对比控制栏 */}\n            <div style={{\n              height: '32px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              marginBottom: '12px',\n              fontSize: '12px',\n              color: '#aaa'\n            }}>\n              <span>并排对比模式</span>\n\n              {/* 缩放控制按钮 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '4px',\n                position: 'relative',\n                zIndex: 1000 // 确保按钮始终在最上层\n              }}>\n                <button\n                  onClick={zoomOut}\n                  disabled={zoomLevel <= 0.5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel <= 0.5 ? '#444' : '#555',\n                    color: zoomLevel <= 0.5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel <= 0.5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"缩小\"\n                >\n                  −\n                </button>\n\n                <span style={{\n                  minWidth: '40px',\n                  textAlign: 'center',\n                  fontSize: '11px',\n                  color: zoomLevel === 1 ? '#aaa' : '#4a90e2'\n                }}>\n                  {(zoomLevel * 100).toFixed(0)}%\n                </span>\n\n                <button\n                  onClick={zoomIn}\n                  disabled={zoomLevel >= 5}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: zoomLevel >= 5 ? '#444' : '#555',\n                    color: zoomLevel >= 5 ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: zoomLevel >= 5 ? 'not-allowed' : 'pointer',\n                    fontSize: '12px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"放大\"\n                >\n                  +\n                </button>\n\n                <button\n                  onClick={resetZoom}\n                  disabled={zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0}\n                  style={{\n                    width: '24px',\n                    height: '24px',\n                    backgroundColor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#444' : '#555',\n                    color: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? '#666' : '#fff',\n                    border: '1px solid #666',\n                    borderRadius: '3px',\n                    cursor: (zoomLevel === 1 && panOffset.x === 0 && panOffset.y === 0) ? 'not-allowed' : 'pointer',\n                    fontSize: '10px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    position: 'relative',\n                    zIndex: 1001\n                  }}\n                  title=\"重置缩放\"\n                >\n                  ⌂\n                </button>\n              </div>\n            </div>\n\n            {/* 并排图像容器 */}\n            <div style={{\n              flex: 1,\n              display: 'flex',\n              gap: '16px',\n              alignItems: 'center',\n              justifyContent: 'center',\n              overflow: 'auto' // 添加滚动条支持\n            }}>\n            {/* 原始图像 */}\n            {originalImage && (\n              <div style={{ \n                flex: '1', \n                textAlign: 'center',\n                maxWidth: '50%'\n              }}>\n                <div style={{ \n                  marginBottom: '8px', \n                  fontSize: '12px', \n                  color: '#aaa' \n                }}>\n                  原始图像\n                </div>\n                <div\n                  style={{\n                    display: 'inline-block',\n                    transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                    transformOrigin: 'center center',\n                    transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                    cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                  }}\n                  onWheel={handleWheel}\n                  onMouseDown={handleImageMouseDown}\n                >\n                  <img\n                    src={originalImage}\n                    alt=\"原始图像\"\n                    style={{\n                      maxWidth: '100%',\n                      maxHeight: 'calc(100vh - 300px)',\n                      height: 'auto',\n                      border: '1px solid #4a90e2',\n                      borderRadius: '4px',\n                      objectFit: 'contain',\n                      imageRendering: 'pixelated' // 最近邻插值，保持像素清晰\n                    }}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* 增强图像 */}\n            <div style={{ \n              flex: '1', \n              textAlign: 'center',\n              maxWidth: '50%'\n            }}>\n              <div style={{ \n                marginBottom: '8px', \n                fontSize: '12px', \n                color: '#aaa' \n              }}>\n                增强图像\n              </div>\n\n              {!imageLoaded && !imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px dashed #666',\n                  borderRadius: '4px',\n                  color: '#aaa',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  正在加载图像...\n                </div>\n              )}\n\n              {imageError && (\n                <div style={{\n                  width: '100%',\n                  height: '200px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  border: '1px solid #ff6b6b',\n                  borderRadius: '4px',\n                  color: '#ff6b6b',\n                  backgroundColor: '#2b2b2b'\n                }}>\n                  图像加载失败\n                </div>\n              )}\n\n              <div\n                style={{\n                  display: imageLoaded ? 'inline-block' : 'none',\n                  transform: `scale(${zoomLevel}) translate(${panOffset.x / zoomLevel}px, ${panOffset.y / zoomLevel}px)`,\n                  transformOrigin: 'center center',\n                  transition: isPanning ? 'none' : 'transform 0.2s ease-out',\n                  cursor: zoomLevel > 1 ? (isPanning ? 'grabbing' : 'grab') : 'default'\n                }}\n                onWheel={handleWheel}\n                onMouseDown={handleImageMouseDown}\n              >\n                <img\n                  src={`${apiBaseUrl}/result/${result.enhanced_url}?t=${Date.now()}`}\n                  alt=\"增强图像\"\n                  onLoad={handleImageLoad}\n                  onError={handleImageError}\n                  style={{\n                    maxWidth: '100%',\n                    maxHeight: 'calc(100vh - 300px)',\n                    height: 'auto',\n                    border: '1px solid #28ca42',\n                    borderRadius: '4px',\n                    objectFit: 'contain'\n                  }}\n                />\n              </div>\n            </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 底部控制栏 */}\n      {imageLoaded && (\n        <div style={{\n          height: '48px',\n          backgroundColor: '#333',\n          borderTop: '1px solid #555',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          gap: '12px',\n          padding: '0 16px',\n          flexShrink: 0\n        }}>\n          <button\n            onClick={downloadImage}\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#28ca42',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#22a83a'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#28ca42'}\n          >\n            <span>📥</span>\n            下载图像\n          </button>\n\n          <a\n            href={`${apiBaseUrl}/result/${result.enhanced_url}`}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            style={{\n              padding: '6px 12px',\n              backgroundColor: '#4a90e2',\n              color: 'white',\n              textDecoration: 'none',\n              borderRadius: '4px',\n              fontSize: '13px',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '6px',\n              transition: 'background-color 0.2s'\n            }}\n            onMouseEnter={(e) => e.target.style.backgroundColor = '#357abd'}\n            onMouseLeave={(e) => e.target.style.backgroundColor = '#4a90e2'}\n          >\n            <span>🔍</span>\n            新窗口查看\n          </a>\n\n          <div style={{\n            fontSize: '12px',\n            color: '#aaa',\n            marginLeft: 'auto'\n          }}>\n            文件: {result.filename}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ResultView;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,OAASC,SAAS,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAA+B,IAA9B,CAAEC,MAAM,CAAEC,aAAc,CAAC,CAAAF,IAAA,CAC3C,KAAM,CAACG,WAAW,CAAEC,cAAc,CAAC,CAAGb,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACc,UAAU,CAAEC,aAAa,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACgB,UAAU,CAAEC,aAAa,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACkB,UAAU,CAAEC,aAAa,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACoB,aAAa,CAAEC,gBAAgB,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAAE;AACxD,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACwB,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAC,OAAO,CAAC,CAAE;AACnD,KAAM,CAAC0B,SAAS,CAAEC,YAAY,CAAC,CAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAE;AAC/C,KAAM,CAAC4B,SAAS,CAAEC,YAAY,CAAC,CAAG7B,QAAQ,CAAC,CAAE8B,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAAE;AAC5D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACkC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAC,CAAE8B,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAChE,KAAM,CAAAK,YAAY,CAAGnC,MAAM,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAoC,gBAAgB,CAAGpC,MAAM,CAAC,IAAI,CAAC,CACrC,KAAM,CAAAqC,iBAAiB,CAAGrC,MAAM,CAAC,IAAI,CAAC,CAEtC;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqC,UAAU,CAAG,KAAAA,CAAA,GAAY,CAC7B,GAAI,CACF,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAArC,SAAS,CAAC,EAAE,CAAC,CACnCc,aAAa,CAACuB,OAAO,CAAC,CACxB,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CACpC,CACF,CAAC,CACDF,UAAU,CAAC,CAAC,CACd,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAI,eAAe,CAAGA,CAAA,GAAM,CAC5B9B,cAAc,CAAC,IAAI,CAAC,CACpBE,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED,KAAM,CAAA6B,gBAAgB,CAAGA,CAAA,GAAM,CAC7B7B,aAAa,CAAC,IAAI,CAAC,CACnBF,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,KAAM,CAAAgC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAG,GAAGjC,UAAU,WAAWN,MAAM,CAACwC,YAAY,EAAE,CACzDJ,IAAI,CAACK,QAAQ,CAAG,YAAYzC,MAAM,CAAC0C,QAAQ,EAAE,CAC7CL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC,CAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC,CACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAW,oBAAoB,CAAIC,CAAC,EAAK,CAClCnC,aAAa,CAAC,IAAI,CAAC,CACnBmC,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CAAE;AACvB,CAAC,CAED,KAAM,CAAAC,oBAAoB,CAAIH,CAAC,EAAK,CAClC,GAAI,CAACpC,UAAU,EAAI,CAACgB,iBAAiB,CAACwB,OAAO,CAAE,OAE/C,KAAM,CAAAC,IAAI,CAAGzB,iBAAiB,CAACwB,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC9D,KAAM,CAAAlC,CAAC,CAAG4B,CAAC,CAACO,OAAO,CAAGF,IAAI,CAACG,IAAI,CAC/B,KAAM,CAAAC,UAAU,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAAC,GAAG,CAAGxC,CAAC,CAAGiC,IAAI,CAACQ,KAAK,CAAI,GAAG,CAAC,CAAC,CACrElD,gBAAgB,CAAC8C,UAAU,CAAC,CAC9B,CAAC,CAED,KAAM,CAAAK,kBAAkB,CAAGA,CAAA,GAAM,CAC/BjD,aAAa,CAAC,KAAK,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAkD,WAAW,CAAIf,CAAC,EAAK,CACzB,GAAI,CAACpB,iBAAiB,CAACwB,OAAO,CAAE,OAEhCJ,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,KAAM,CAAAe,KAAK,CAAGhB,CAAC,CAACiB,MAAM,CAAG,CAAC,CAAG,CAAC,GAAG,CAAG,GAAG,CACvC,KAAM,CAAAC,OAAO,CAAGR,IAAI,CAACC,GAAG,CAAC,GAAG,CAAED,IAAI,CAACE,GAAG,CAAC,CAAC,CAAE5C,SAAS,CAAGgD,KAAK,CAAC,CAAC,CAC7D/C,YAAY,CAACiD,OAAO,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAInB,CAAC,EAAK,CAC5B,GAAIhC,SAAS,EAAI,CAAC,EAAIJ,UAAU,CAAE,OAAQ;AAE1CW,YAAY,CAAC,IAAI,CAAC,CAClBE,eAAe,CAAC,CACdL,CAAC,CAAE4B,CAAC,CAACO,OAAO,CACZlC,CAAC,CAAE2B,CAAC,CAACoB,OACP,CAAC,CAAC,CACFpB,CAAC,CAACC,cAAc,CAAC,CAAC,CACpB,CAAC,CAED;AACA,KAAM,CAAAoB,oBAAoB,CAAIrB,CAAC,EAAK,CAClC;AACA,GAAIA,CAAC,CAACsB,MAAM,CAACC,OAAO,CAAC,mBAAmB,CAAC,CAAE,CACzC,OAAQ;AACV,CAEA,GAAIvD,SAAS,CAAG,CAAC,EAAI,CAACJ,UAAU,CAAE,CAChCuD,cAAc,CAACnB,CAAC,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAwB,aAAa,CAAIxB,CAAC,EAAK,CAC3B,GAAI,CAAC1B,SAAS,CAAE,OAEhB,KAAM,CAAAmD,MAAM,CAAGzB,CAAC,CAACO,OAAO,CAAG/B,YAAY,CAACJ,CAAC,CACzC,KAAM,CAAA6C,MAAM,CAAGjB,CAAC,CAACoB,OAAO,CAAG5C,YAAY,CAACH,CAAC,CAEzCF,YAAY,CAACuD,IAAI,GAAK,CACpBtD,CAAC,CAAEsD,IAAI,CAACtD,CAAC,CAAGqD,MAAM,CAClBpD,CAAC,CAAEqD,IAAI,CAACrD,CAAC,CAAG4C,MACd,CAAC,CAAC,CAAC,CAEHxC,eAAe,CAAC,CACdL,CAAC,CAAE4B,CAAC,CAACO,OAAO,CACZlC,CAAC,CAAE2B,CAAC,CAACoB,OACP,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAO,YAAY,CAAGA,CAAA,GAAM,CACzBpD,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AACA,KAAM,CAAAqD,SAAS,CAAGA,CAAA,GAAM,CACtB3D,YAAY,CAAC,CAAC,CAAC,CACfE,YAAY,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAC9B,CAAC,CAED;AACA,KAAM,CAAAwD,MAAM,CAAGA,CAAA,GAAM,CACnB5D,YAAY,CAACyD,IAAI,EAAIhB,IAAI,CAACE,GAAG,CAAC,CAAC,CAAEc,IAAI,CAAG,IAAI,CAAC,CAAC,CAChD,CAAC,CAED,KAAM,CAAAI,OAAO,CAAGA,CAAA,GAAM,CACpB7D,YAAY,CAACyD,IAAI,EAAIhB,IAAI,CAACC,GAAG,CAAC,GAAG,CAAEe,IAAI,CAAG,IAAI,CAAC,CAAC,CAClD,CAAC,CAED;AACAlF,SAAS,CAAC,IAAM,CACd,GAAIoB,UAAU,CAAE,CACdyB,QAAQ,CAAC0C,gBAAgB,CAAC,WAAW,CAAE5B,oBAAoB,CAAC,CAC5Dd,QAAQ,CAAC0C,gBAAgB,CAAC,SAAS,CAAEjB,kBAAkB,CAAC,CACxD,MAAO,IAAM,CACXzB,QAAQ,CAAC2C,mBAAmB,CAAC,WAAW,CAAE7B,oBAAoB,CAAC,CAC/Dd,QAAQ,CAAC2C,mBAAmB,CAAC,SAAS,CAAElB,kBAAkB,CAAC,CAC7D,CAAC,CACH,CACF,CAAC,CAAE,CAAClD,UAAU,CAAC,CAAC,CAEhB;AACApB,SAAS,CAAC,IAAM,CACd,GAAI8B,SAAS,CAAE,CACbe,QAAQ,CAAC0C,gBAAgB,CAAC,WAAW,CAAEP,aAAa,CAAC,CACrDnC,QAAQ,CAAC0C,gBAAgB,CAAC,SAAS,CAAEJ,YAAY,CAAC,CAClD,MAAO,IAAM,CACXtC,QAAQ,CAAC2C,mBAAmB,CAAC,WAAW,CAAER,aAAa,CAAC,CACxDnC,QAAQ,CAAC2C,mBAAmB,CAAC,SAAS,CAAEL,YAAY,CAAC,CACvD,CAAC,CACH,CACF,CAAC,CAAE,CAACrD,SAAS,CAAEE,YAAY,CAAC,CAAC,CAI7B;AACA,KAAM,CAAAyD,UAAU,CAAGA,CAAA,GAAM,CACvBtE,gBAAgB,CAAC,EAAE,CAAC,CACtB,CAAC,CAED,mBACEd,KAAA,QAAKqF,KAAK,CAAE,CACVrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,eAEA3F,KAAA,QAAKqF,KAAK,CAAE,CACVC,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,SAAS,CAC1BG,YAAY,CAAE,gBAAgB,CAC9BL,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/BC,OAAO,CAAE,QAAQ,CACjBC,UAAU,CAAE,CACd,CAAE,CAAAL,QAAA,eAEA3F,KAAA,QAAKqF,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEM,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,MAAO,CAAE,CAAAN,QAAA,eACjE7F,IAAA,QAAKuF,KAAK,CAAE,CACVrB,KAAK,CAAE,KAAK,CACZsB,MAAM,CAAE,KAAK,CACbY,YAAY,CAAE,KAAK,CACnBT,eAAe,CAAE,SACnB,CAAE,CAAM,CAAC,cACT3F,IAAA,SAAMuF,KAAK,CAAE,CAAEc,QAAQ,CAAE,MAAM,CAAET,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,CAC5DxF,MAAM,CAACiG,OAAO,eACbpG,KAAA,SAAMqF,KAAK,CAAE,CAAEc,QAAQ,CAAE,MAAM,CAAET,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,EAAC,SAAE,CAACxF,MAAM,CAACiG,OAAO,EAAO,CAC3E,EACE,CAAC,cAGNpG,KAAA,QAAKqF,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEU,GAAG,CAAE,KAAK,CAAEJ,UAAU,CAAE,QAAS,CAAE,CAAAF,QAAA,eAChE7F,IAAA,WACEuG,OAAO,CAAEA,CAAA,GAAMnF,WAAW,CAAC,OAAO,CAAE,CACpCmE,KAAK,CAAE,CACLU,OAAO,CAAE,SAAS,CAClBN,eAAe,CAAExE,QAAQ,GAAK,OAAO,CAAG,SAAS,CAAG,MAAM,CAC1DyE,KAAK,CAAE,OAAO,CACdY,MAAM,CAAE,MAAM,CACdJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAE,SAAS,CACjBJ,QAAQ,CAAE,MAAM,CAChBK,UAAU,CAAE,uBACd,CAAE,CAAAb,QAAA,CACH,0BAED,CAAQ,CAAC,cACT7F,IAAA,WACEuG,OAAO,CAAEA,CAAA,GAAMnF,WAAW,CAAC,cAAc,CAAE,CAC3CmE,KAAK,CAAE,CACLU,OAAO,CAAE,SAAS,CAClBN,eAAe,CAAExE,QAAQ,GAAK,cAAc,CAAG,SAAS,CAAG,MAAM,CACjEyE,KAAK,CAAE,OAAO,CACdY,MAAM,CAAE,MAAM,CACdJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAE,SAAS,CACjBJ,QAAQ,CAAE,MAAM,CAChBK,UAAU,CAAE,uBACd,CAAE,CAAAb,QAAA,CACH,0BAED,CAAQ,CAAC,CACR1E,QAAQ,GAAK,OAAO,eACnBnB,IAAA,WACEuG,OAAO,CAAEjB,UAAW,CACpBC,KAAK,CAAE,CACLU,OAAO,CAAE,SAAS,CAClBN,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdY,MAAM,CAAE,MAAM,CACdJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAE,SAAS,CACjBJ,QAAQ,CAAE,MAAM,CAChBK,UAAU,CAAE,uBACd,CAAE,CAAAb,QAAA,CACH,cAED,CAAQ,CACT,EACE,CAAC,EACH,CAAC,CAGLxF,MAAM,CAACsG,WAAW,eACjBzG,KAAA,QAAKqF,KAAK,CAAE,CACVC,MAAM,CAAE3E,UAAU,CAAG,MAAM,CAAG,MAAM,CACpC8E,eAAe,CAAE,SAAS,CAC1BG,YAAY,CAAE,gBAAgB,CAC9BG,OAAO,CAAE,UAAU,CACnBC,UAAU,CAAE,CAAC,CACbQ,UAAU,CAAE,kBACd,CAAE,CAAAb,QAAA,eACA3F,KAAA,QAAKqF,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEM,UAAU,CAAE,QAAQ,CAAEC,cAAc,CAAE,eAAgB,CAAE,CAAAH,QAAA,eACrF3F,KAAA,QAAKqF,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEM,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,MAAO,CAAE,CAAAN,QAAA,eACjE7F,IAAA,SAAMuF,KAAK,CAAE,CAAEc,QAAQ,CAAE,MAAM,CAAET,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,CAC7D,CAAChF,UAAU,eACVX,KAAA,QAAKqF,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEU,GAAG,CAAE,MAAM,CAAEE,QAAQ,CAAE,MAAM,CAAET,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,eAC5E3F,KAAA,SAAA2F,QAAA,EAAOxF,MAAM,CAACsG,WAAW,CAACC,KAAK,CAAC,eAAG,EAAM,CAAC,cAC1C5G,IAAA,SAAA6F,QAAA,CAAOxF,MAAM,CAACsG,WAAW,CAACE,cAAc,CAAG,YAAY,CAAG,IAAI,CAAO,CAAC,cACtE3G,KAAA,SAAA2F,QAAA,EAAM,cAAE,CAAC,CAACxF,MAAM,CAACsG,WAAW,CAACG,UAAU,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,cAClE7G,KAAA,SAAA2F,QAAA,EAAM,cAAE,CAAC,CAACxF,MAAM,CAACsG,WAAW,CAACK,MAAM,CAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,EAC3D,CACN,EACE,CAAC,cACN/G,IAAA,WACEuG,OAAO,CAAEA,CAAA,GAAMzF,aAAa,CAAC,CAACD,UAAU,CAAE,CAC1C0E,KAAK,CAAE,CACLU,OAAO,CAAE,SAAS,CAClBN,eAAe,CAAE,aAAa,CAC9BC,KAAK,CAAE,MAAM,CACbY,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAE,SAAS,CACjBJ,QAAQ,CAAE,MAAM,CAChBK,UAAU,CAAE,UACd,CAAE,CACFO,YAAY,CAAG5D,CAAC,EAAK,CACnBA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,CAAG,MAAM,CACvCtC,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACK,KAAK,CAAG,MAAM,CAC/B,CAAE,CACFsB,YAAY,CAAG7D,CAAC,EAAK,CACnBA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,CAAG,aAAa,CAC9CtC,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACK,KAAK,CAAG,MAAM,CAC/B,CAAE,CAAAC,QAAA,CAEDhF,UAAU,CAAG,GAAG,CAAG,GAAG,CACjB,CAAC,EACN,CAAC,CAELA,UAAU,eACTX,KAAA,QAAKqF,KAAK,CAAE,CACV4B,SAAS,CAAE,MAAM,CACjB1B,OAAO,CAAE,MAAM,CACf2B,mBAAmB,CAAE,sCAAsC,CAC3DjB,GAAG,CAAE,KAAK,CACVE,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,eACA3F,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,IAAC,CAACxF,MAAM,CAACsG,WAAW,CAACC,KAAK,CAAC,GAAC,EAAK,CAAC,cACnF1G,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,iBAAK,CAAM,CAAC,IAAC,CAACxF,MAAM,CAACsG,WAAW,CAACE,cAAc,CAAG,YAAY,CAAG,MAAM,EAAM,CAAC,cACnH3G,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,IAAC,CAAC,CAACxF,MAAM,CAACsG,WAAW,CAACG,UAAU,CAAG,GAAG,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,cACzG7G,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,IAAC,CAACxF,MAAM,CAACsG,WAAW,CAACU,SAAS,EAAM,CAAC,cACpFnH,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,qBAAI,CAAM,CAAC,IAAC,CAACxF,MAAM,CAACsG,WAAW,CAACW,UAAU,CAACP,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cACjG7G,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,qBAAI,CAAM,CAAC,IAAC,CAACxF,MAAM,CAACsG,WAAW,CAACY,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC,EAAM,CAAC,cAC/F7G,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,IAAC,CAACxF,MAAM,CAACsG,WAAW,CAACa,UAAU,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,CAAEnH,MAAM,CAACsG,WAAW,CAACa,UAAU,EAAM,CAAC,cACnItH,KAAA,QAAA2F,QAAA,eAAK7F,IAAA,SAAMuF,KAAK,CAAE,CAAEK,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAAC,eAAG,CAAM,CAAC,IAAC,CAAC,CAACxF,MAAM,CAACsG,WAAW,CAACK,MAAM,CAAG,GAAG,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAK,CAAC,EAClG,CACN,EACE,CACN,cAGD/G,IAAA,QAAKuF,KAAK,CAAE,CACVkC,IAAI,CAAE,CAAC,CACPhC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBgC,QAAQ,CAAE,QAAQ,CAClBC,QAAQ,CAAE,UACZ,CAAE,CAAA9B,QAAA,CACC1E,QAAQ,GAAK,OAAO,cACnB;AACAjB,KAAA,QAAKqF,KAAK,CAAE,CACVkC,IAAI,CAAE,CAAC,CACPhC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBO,OAAO,CAAE,MACX,CAAE,CAAAJ,QAAA,eAEA3F,KAAA,QAAKqF,KAAK,CAAE,CACVC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/B4B,YAAY,CAAE,MAAM,CACpBvB,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,eACA7F,IAAA,QAAKuF,KAAK,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAEM,UAAU,CAAE,QAAQ,CAAEI,GAAG,CAAE,KAAM,CAAE,CAAAN,QAAA,cAChE3F,KAAA,SAAA2F,QAAA,EAAM,0HAAyB,CAAC9E,aAAa,CAACgG,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,EAAM,CAAC,CAC9D,CAAC,cAGN7G,KAAA,QAAKqF,KAAK,CAAE,CACVE,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBI,GAAG,CAAE,KAAK,CACVwB,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IAAK;AACf,CAAE,CAAAhC,QAAA,eACA7F,IAAA,WACEuG,OAAO,CAAEpB,OAAQ,CACjB2C,QAAQ,CAAEzG,SAAS,EAAI,GAAI,CAC3BkE,KAAK,CAAE,CACLrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAEtE,SAAS,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACnDuE,KAAK,CAAEvE,SAAS,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACzCmF,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAEpF,SAAS,EAAI,GAAG,CAAG,aAAa,CAAG,SAAS,CACpDgF,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB2B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IACV,CAAE,CACFE,KAAK,CAAC,cAAI,CAAAlC,QAAA,CACX,QAED,CAAQ,CAAC,cAET3F,KAAA,SAAMqF,KAAK,CAAE,CACXyC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,QAAQ,CACnB5B,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAEvE,SAAS,GAAK,CAAC,CAAG,MAAM,CAAG,SACpC,CAAE,CAAAwE,QAAA,EACC,CAACxE,SAAS,CAAG,GAAG,EAAE0F,OAAO,CAAC,CAAC,CAAC,CAAC,GAChC,EAAM,CAAC,cAEP/G,IAAA,WACEuG,OAAO,CAAErB,MAAO,CAChB4C,QAAQ,CAAEzG,SAAS,EAAI,CAAE,CACzBkE,KAAK,CAAE,CACLrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAEtE,SAAS,EAAI,CAAC,CAAG,MAAM,CAAG,MAAM,CACjDuE,KAAK,CAAEvE,SAAS,EAAI,CAAC,CAAG,MAAM,CAAG,MAAM,CACvCmF,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAEpF,SAAS,EAAI,CAAC,CAAG,aAAa,CAAG,SAAS,CAClDgF,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB2B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IACV,CAAE,CACFE,KAAK,CAAC,cAAI,CAAAlC,QAAA,CACX,GAED,CAAQ,CAAC,cAET7F,IAAA,WACEuG,OAAO,CAAEtB,SAAU,CACnB6C,QAAQ,CAAEzG,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAE,CACpE6D,KAAK,CAAE,CACLrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAGtE,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAC,CAAI,MAAM,CAAG,MAAM,CAC9FkE,KAAK,CAAGvE,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAC,CAAI,MAAM,CAAG,MAAM,CACpF8E,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAGpF,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAC,CAAI,aAAa,CAAG,SAAS,CAC/F2E,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB2B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IACV,CAAE,CACFE,KAAK,CAAC,0BAAM,CAAAlC,QAAA,CACb,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN7F,IAAA,QACEkI,GAAG,CAAEnG,YAAa,CAClBwD,KAAK,CAAE,CACLoC,QAAQ,CAAE,UAAU,CACpBzD,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdiB,MAAM,CAAExF,UAAU,CAAG,WAAW,CAAG,SAAS,CAC5CkH,UAAU,CAAE,MAAM,CAClB1C,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB0B,QAAQ,CAAE,MAAO;AACnB,CAAE,CAAA7B,QAAA,CAEDvF,aAAa,eACZN,IAAA,QAAKuF,KAAK,CAAE,CACVoC,QAAQ,CAAE,UAAU,CACpBzD,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACd4C,SAAS,CAAE,qBAAqB,CAChC3C,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAH,QAAA,cAEA3F,KAAA,QACEgI,GAAG,CAAEjG,iBAAkB,CACvBsD,KAAK,CAAE,CACLoC,QAAQ,CAAE,UAAU,CACpBlC,OAAO,CAAE,cAAc,CACvBe,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBsB,QAAQ,CAAE,QAAQ,CAClBW,SAAS,CAAE,SAAShH,SAAS,eAAeE,SAAS,CAACE,CAAC,CAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,CAAGL,SAAS,KAAK,CACtGiH,eAAe,CAAE,eAAe,CAChC5B,UAAU,CAAE/E,SAAS,CAAG,MAAM,CAAG,yBAAyB,CAC1D8E,MAAM,CAAEpF,SAAS,CAAG,CAAC,CAAIM,SAAS,CAAG,UAAU,CAAG,MAAM,CAAI,SAC9D,CAAE,CACF4G,OAAO,CAAEnE,WAAY,CACrBoE,WAAW,CAAE9D,oBAAqB,CAAAmB,QAAA,EAGjCpF,UAAU,cACTT,IAAA,QAAKuF,KAAK,CAAE,CACVrB,KAAK,CAAE,OAAO,CACdsB,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBJ,KAAK,CAAE,SAAS,CAChBD,eAAe,CAAE,SACnB,CAAE,CAAAE,QAAA,CAAC,kDAEH,CAAK,CAAC,cAEN7F,IAAA,QACEkI,GAAG,CAAElG,gBAAiB,CACtByG,GAAG,CAAE,GAAG9H,UAAU,WAAWN,MAAM,CAACwC,YAAY,MAAM6F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CACnEC,GAAG,CAAC,0BAAM,CACVC,MAAM,CAAEvG,eAAgB,CACxBwG,OAAO,CAAEvG,gBAAiB,CAC1BgD,KAAK,CAAE,CACLwD,QAAQ,CAAE,MAAM,CAChBX,SAAS,CAAE,qBAAqB,CAChC5C,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,OAAO,CAChBuD,OAAO,CAAEzI,WAAW,CAAG,CAAC,CAAG,GAAG,CAC9B0I,cAAc,CAAE,MAAO;AACzB,CAAE,CACH,CACF,CAGA1I,WAAW,eACVP,IAAA,QACEuF,KAAK,CAAE,CACLoC,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNrF,IAAI,CAAE,CAAC,CACPK,KAAK,CAAE,GAAGnD,aAAa,GAAG,CAC1ByE,MAAM,CAAE,MAAM,CACdkC,QAAQ,CAAE,QACZ,CAAE,CAAA7B,QAAA,cAEF7F,IAAA,QACEyI,GAAG,CAAEnI,aAAc,CACnBsI,GAAG,CAAC,0BAAM,CACVrD,KAAK,CAAE,CACL;AACArB,KAAK,CAAE,GAAG,GAAG,CAAG,GAAG,CAAGnD,aAAa,GAAG,CACtCyE,MAAM,CAAE,MAAM,CACd2D,SAAS,CAAE,MAAM,CAAE;AACnBF,cAAc,CAAE,WAAW,CAAE;AAC7BtB,QAAQ,CAAE,UAAU,CACpB9D,IAAI,CAAE,CAAC,CACPqF,GAAG,CAAE,CACP,CAAE,CACH,CAAC,CACC,CACN,CAGA3I,WAAW,eACVP,IAAA,QACE,kBAAgB,MAAM,CACtBuF,KAAK,CAAE,CACLoC,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNrF,IAAI,CAAE,GAAG9C,aAAa,GAAG,CACzBmD,KAAK,CAAE,KAAK,CACZsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,SAAS,CAC1Bc,MAAM,CAAE,WAAW,CACnB2C,SAAS,CAAE,iCAAiC,CAC5Cf,SAAS,CAAE,kBAAkB,CAC7BR,MAAM,CAAE,EACV,CAAE,CACFW,WAAW,CAAEpF,oBAAqB,CAAAyC,QAAA,cAGlC7F,IAAA,QACEuF,KAAK,CAAE,CACLoC,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,KAAK,CACVrF,IAAI,CAAE,KAAK,CACXwE,SAAS,CAAE,uBAAuB,CAClCnE,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,SAAS,CAC1BS,YAAY,CAAE,KAAK,CACnBX,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBJ,KAAK,CAAE,OAAO,CACdS,QAAQ,CAAE,MAAM,CAChBgD,UAAU,CAAE,MAAM,CAClBD,SAAS,CAAE,2BACb,CAAE,CAAAvD,QAAA,CACH,QAED,CAAK,CAAC,CACH,CACN,CAGA,CAACtF,WAAW,EAAI,CAACE,UAAU,eAC1BT,IAAA,QAAKuF,KAAK,CAAE,CACVoC,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,KAAK,CACVrF,IAAI,CAAE,KAAK,CACXwE,SAAS,CAAE,uBAAuB,CAClCzC,KAAK,CAAE,MAAM,CACbD,eAAe,CAAE,oBAAoB,CACrCM,OAAO,CAAE,UAAU,CACnBG,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,MACZ,CAAE,CAAAR,QAAA,CAAC,uBAEH,CAAK,CACN,EACE,CAAC,CACH,CACN,CACE,CAAC,EACH,CAAC,cAEN;AACA3F,KAAA,QAAKqF,KAAK,CAAE,CACVkC,IAAI,CAAE,CAAC,CACPhC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBO,OAAO,CAAE,MACX,CAAE,CAAAJ,QAAA,eAEA3F,KAAA,QAAKqF,KAAK,CAAE,CACVC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,eAAe,CAC/B4B,YAAY,CAAE,MAAM,CACpBvB,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,eACA7F,IAAA,SAAA6F,QAAA,CAAM,sCAAM,CAAM,CAAC,cAGnB3F,KAAA,QAAKqF,KAAK,CAAE,CACVE,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBI,GAAG,CAAE,KAAK,CACVwB,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IAAK;AACf,CAAE,CAAAhC,QAAA,eACA7F,IAAA,WACEuG,OAAO,CAAEpB,OAAQ,CACjB2C,QAAQ,CAAEzG,SAAS,EAAI,GAAI,CAC3BkE,KAAK,CAAE,CACLrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAEtE,SAAS,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACnDuE,KAAK,CAAEvE,SAAS,EAAI,GAAG,CAAG,MAAM,CAAG,MAAM,CACzCmF,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAEpF,SAAS,EAAI,GAAG,CAAG,aAAa,CAAG,SAAS,CACpDgF,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB2B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IACV,CAAE,CACFE,KAAK,CAAC,cAAI,CAAAlC,QAAA,CACX,QAED,CAAQ,CAAC,cAET3F,KAAA,SAAMqF,KAAK,CAAE,CACXyC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,QAAQ,CACnB5B,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAEvE,SAAS,GAAK,CAAC,CAAG,MAAM,CAAG,SACpC,CAAE,CAAAwE,QAAA,EACC,CAACxE,SAAS,CAAG,GAAG,EAAE0F,OAAO,CAAC,CAAC,CAAC,CAAC,GAChC,EAAM,CAAC,cAEP/G,IAAA,WACEuG,OAAO,CAAErB,MAAO,CAChB4C,QAAQ,CAAEzG,SAAS,EAAI,CAAE,CACzBkE,KAAK,CAAE,CACLrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAEtE,SAAS,EAAI,CAAC,CAAG,MAAM,CAAG,MAAM,CACjDuE,KAAK,CAAEvE,SAAS,EAAI,CAAC,CAAG,MAAM,CAAG,MAAM,CACvCmF,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAEpF,SAAS,EAAI,CAAC,CAAG,aAAa,CAAG,SAAS,CAClDgF,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB2B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IACV,CAAE,CACFE,KAAK,CAAC,cAAI,CAAAlC,QAAA,CACX,GAED,CAAQ,CAAC,cAET7F,IAAA,WACEuG,OAAO,CAAEtB,SAAU,CACnB6C,QAAQ,CAAEzG,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAE,CACpE6D,KAAK,CAAE,CACLrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,MAAM,CACdG,eAAe,CAAGtE,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAC,CAAI,MAAM,CAAG,MAAM,CAC9FkE,KAAK,CAAGvE,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAC,CAAI,MAAM,CAAG,MAAM,CACpF8E,MAAM,CAAE,gBAAgB,CACxBJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAGpF,SAAS,GAAK,CAAC,EAAIE,SAAS,CAACE,CAAC,GAAK,CAAC,EAAIF,SAAS,CAACG,CAAC,GAAK,CAAC,CAAI,aAAa,CAAG,SAAS,CAC/F2E,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB2B,QAAQ,CAAE,UAAU,CACpBE,MAAM,CAAE,IACV,CAAE,CACFE,KAAK,CAAC,0BAAM,CAAAlC,QAAA,CACb,QAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGN3F,KAAA,QAAKqF,KAAK,CAAE,CACVkC,IAAI,CAAE,CAAC,CACPhC,OAAO,CAAE,MAAM,CACfU,GAAG,CAAE,MAAM,CACXJ,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB0B,QAAQ,CAAE,MAAO;AACnB,CAAE,CAAA7B,QAAA,EAEDvF,aAAa,eACZJ,KAAA,QAAKqF,KAAK,CAAE,CACVkC,IAAI,CAAE,GAAG,CACTQ,SAAS,CAAE,QAAQ,CACnBc,QAAQ,CAAE,KACZ,CAAE,CAAAlD,QAAA,eACA7F,IAAA,QAAKuF,KAAK,CAAE,CACVqC,YAAY,CAAE,KAAK,CACnBvB,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,CAAC,0BAEH,CAAK,CAAC,cACN7F,IAAA,QACEuF,KAAK,CAAE,CACLE,OAAO,CAAE,cAAc,CACvB4C,SAAS,CAAE,SAAShH,SAAS,eAAeE,SAAS,CAACE,CAAC,CAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,CAAGL,SAAS,KAAK,CACtGiH,eAAe,CAAE,eAAe,CAChC5B,UAAU,CAAE/E,SAAS,CAAG,MAAM,CAAG,yBAAyB,CAC1D8E,MAAM,CAAEpF,SAAS,CAAG,CAAC,CAAIM,SAAS,CAAG,UAAU,CAAG,MAAM,CAAI,SAC9D,CAAE,CACF4G,OAAO,CAAEnE,WAAY,CACrBoE,WAAW,CAAE9D,oBAAqB,CAAAmB,QAAA,cAElC7F,IAAA,QACEyI,GAAG,CAAEnI,aAAc,CACnBsI,GAAG,CAAC,0BAAM,CACVrD,KAAK,CAAE,CACLwD,QAAQ,CAAE,MAAM,CAChBX,SAAS,CAAE,qBAAqB,CAChC5C,MAAM,CAAE,MAAM,CACdgB,MAAM,CAAE,mBAAmB,CAC3BJ,YAAY,CAAE,KAAK,CACnB+C,SAAS,CAAE,SAAS,CACpBF,cAAc,CAAE,WAAY;AAC9B,CAAE,CACH,CAAC,CACC,CAAC,EACH,CACN,cAGD/I,KAAA,QAAKqF,KAAK,CAAE,CACVkC,IAAI,CAAE,GAAG,CACTQ,SAAS,CAAE,QAAQ,CACnBc,QAAQ,CAAE,KACZ,CAAE,CAAAlD,QAAA,eACA7F,IAAA,QAAKuF,KAAK,CAAE,CACVqC,YAAY,CAAE,KAAK,CACnBvB,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAE,MACT,CAAE,CAAAC,QAAA,CAAC,0BAEH,CAAK,CAAC,CAEL,CAACtF,WAAW,EAAI,CAACE,UAAU,eAC1BT,IAAA,QAAKuF,KAAK,CAAE,CACVrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBQ,MAAM,CAAE,iBAAiB,CACzBJ,YAAY,CAAE,KAAK,CACnBR,KAAK,CAAE,MAAM,CACbD,eAAe,CAAE,SACnB,CAAE,CAAAE,QAAA,CAAC,yCAEH,CAAK,CACN,CAEApF,UAAU,eACTT,IAAA,QAAKuF,KAAK,CAAE,CACVrB,KAAK,CAAE,MAAM,CACbsB,MAAM,CAAE,OAAO,CACfC,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBQ,MAAM,CAAE,mBAAmB,CAC3BJ,YAAY,CAAE,KAAK,CACnBR,KAAK,CAAE,SAAS,CAChBD,eAAe,CAAE,SACnB,CAAE,CAAAE,QAAA,CAAC,sCAEH,CAAK,CACN,cAED7F,IAAA,QACEuF,KAAK,CAAE,CACLE,OAAO,CAAElF,WAAW,CAAG,cAAc,CAAG,MAAM,CAC9C8H,SAAS,CAAE,SAAShH,SAAS,eAAeE,SAAS,CAACE,CAAC,CAAGJ,SAAS,OAAOE,SAAS,CAACG,CAAC,CAAGL,SAAS,KAAK,CACtGiH,eAAe,CAAE,eAAe,CAChC5B,UAAU,CAAE/E,SAAS,CAAG,MAAM,CAAG,yBAAyB,CAC1D8E,MAAM,CAAEpF,SAAS,CAAG,CAAC,CAAIM,SAAS,CAAG,UAAU,CAAG,MAAM,CAAI,SAC9D,CAAE,CACF4G,OAAO,CAAEnE,WAAY,CACrBoE,WAAW,CAAE9D,oBAAqB,CAAAmB,QAAA,cAElC7F,IAAA,QACEyI,GAAG,CAAE,GAAG9H,UAAU,WAAWN,MAAM,CAACwC,YAAY,MAAM6F,IAAI,CAACC,GAAG,CAAC,CAAC,EAAG,CACnEC,GAAG,CAAC,0BAAM,CACVC,MAAM,CAAEvG,eAAgB,CACxBwG,OAAO,CAAEvG,gBAAiB,CAC1BgD,KAAK,CAAE,CACLwD,QAAQ,CAAE,MAAM,CAChBX,SAAS,CAAE,qBAAqB,CAChC5C,MAAM,CAAE,MAAM,CACdgB,MAAM,CAAE,mBAAmB,CAC3BJ,YAAY,CAAE,KAAK,CACnB+C,SAAS,CAAE,SACb,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,EACD,CAAC,EACH,CACN,CACE,CAAC,CAGL5I,WAAW,eACVL,KAAA,QAAKqF,KAAK,CAAE,CACVC,MAAM,CAAE,MAAM,CACdG,eAAe,CAAE,MAAM,CACvB2D,SAAS,CAAE,gBAAgB,CAC3B7D,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBG,GAAG,CAAE,MAAM,CACXF,OAAO,CAAE,QAAQ,CACjBC,UAAU,CAAE,CACd,CAAE,CAAAL,QAAA,eACA3F,KAAA,WACEqG,OAAO,CAAE/D,aAAc,CACvB+C,KAAK,CAAE,CACLU,OAAO,CAAE,UAAU,CACnBN,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACdY,MAAM,CAAE,MAAM,CACdJ,YAAY,CAAE,KAAK,CACnBK,MAAM,CAAE,SAAS,CACjBJ,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBI,GAAG,CAAE,KAAK,CACVO,UAAU,CAAE,uBACd,CAAE,CACFO,YAAY,CAAG5D,CAAC,EAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,CAAG,SAAU,CAChEuB,YAAY,CAAG7D,CAAC,EAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,CAAG,SAAU,CAAAE,QAAA,eAEhE7F,IAAA,SAAA6F,QAAA,CAAM,cAAE,CAAM,CAAC,2BAEjB,EAAQ,CAAC,cAET3F,KAAA,MACE0C,IAAI,CAAE,GAAGjC,UAAU,WAAWN,MAAM,CAACwC,YAAY,EAAG,CACpD8B,MAAM,CAAC,QAAQ,CACf4E,GAAG,CAAC,qBAAqB,CACzBhE,KAAK,CAAE,CACLU,OAAO,CAAE,UAAU,CACnBN,eAAe,CAAE,SAAS,CAC1BC,KAAK,CAAE,OAAO,CACd4D,cAAc,CAAE,MAAM,CACtBpD,YAAY,CAAE,KAAK,CACnBC,QAAQ,CAAE,MAAM,CAChBZ,OAAO,CAAE,MAAM,CACfM,UAAU,CAAE,QAAQ,CACpBI,GAAG,CAAE,KAAK,CACVO,UAAU,CAAE,uBACd,CAAE,CACFO,YAAY,CAAG5D,CAAC,EAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,CAAG,SAAU,CAChEuB,YAAY,CAAG7D,CAAC,EAAKA,CAAC,CAACsB,MAAM,CAACY,KAAK,CAACI,eAAe,CAAG,SAAU,CAAAE,QAAA,eAEhE7F,IAAA,SAAA6F,QAAA,CAAM,cAAE,CAAM,CAAC,iCAEjB,EAAG,CAAC,cAEJ3F,KAAA,QAAKqF,KAAK,CAAE,CACVc,QAAQ,CAAE,MAAM,CAChBT,KAAK,CAAE,MAAM,CACb6D,UAAU,CAAE,MACd,CAAE,CAAA5D,QAAA,EAAC,gBACG,CAACxF,MAAM,CAAC0C,QAAQ,EACjB,CAAC,EACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA5C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}