{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { getApiUrl } from './config/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const uploadFormRef = useRef(null);\n  const testApiDiscovery = async () => {\n    try {\n      setIsLoading(true);\n      const apiUrl = await getApiUrl('/');\n      alert(`API发现成功: ${apiUrl}`);\n    } catch (error) {\n      alert(`API发现失败: ${error.message}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5E94\\u7528 - API\\u6D4B\\u8BD5\\u7248\\u672C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\u6D4B\\u8BD5API\\u53D1\\u73B0\\u529F\\u80FD\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f0f0f0',\n        padding: '15px',\n        borderRadius: '5px',\n        margin: '20px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u72B6\\u6001\\u4FE1\\u606F:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u52A0\\u8F7D\\u72B6\\u6001: \", isLoading ? '是' : '否']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u9519\\u8BEF\\u4FE1\\u606F: \", error || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u7ED3\\u679C: \", result ? '有数据' : '无数据']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: testApiDiscovery,\n      disabled: isLoading,\n      style: {\n        padding: '10px 20px',\n        backgroundColor: isLoading ? '#666' : '#007bff',\n        color: 'white',\n        border: 'none',\n        borderRadius: '4px',\n        cursor: isLoading ? 'not-allowed' : 'pointer',\n        marginRight: '10px'\n      },\n      children: isLoading ? '测试中...' : '测试API发现'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '12px',\n        backgroundColor: '#ff4444',\n        color: '#fff',\n        margin: '8px 0',\n        borderRadius: '4px',\n        fontSize: '14px'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"AoUWqesfEKvLS+60WoOWtYzxPKw=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "getApiUrl", "jsxDEV", "_jsxDEV", "App", "_s", "result", "setResult", "isLoading", "setIsLoading", "error", "setError", "originalImage", "setOriginalImage", "uploadFormRef", "testApiDiscovery", "apiUrl", "alert", "message", "style", "padding", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "borderRadius", "margin", "onClick", "disabled", "color", "border", "cursor", "marginRight", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useRef } from 'react';\nimport { getApiUrl } from './config/api';\n\nfunction App() {\n  const [result, setResult] = useState(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [originalImage, setOriginalImage] = useState(null);\n  const uploadFormRef = useRef(null);\n\n  const testApiDiscovery = async () => {\n    try {\n      setIsLoading(true);\n      const apiUrl = await getApiUrl('/');\n      alert(`API发现成功: ${apiUrl}`);\n    } catch (error) {\n      alert(`API发现失败: ${error.message}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={{\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    }}>\n      <h1>图像增强应用 - API测试版本</h1>\n      <p>测试API发现功能</p>\n\n      <div style={{\n        backgroundColor: '#f0f0f0',\n        padding: '15px',\n        borderRadius: '5px',\n        margin: '20px 0'\n      }}>\n        <h3>状态信息:</h3>\n        <ul>\n          <li>加载状态: {isLoading ? '是' : '否'}</li>\n          <li>错误信息: {error || '无'}</li>\n          <li>结果: {result ? '有数据' : '无数据'}</li>\n        </ul>\n      </div>\n\n      <button\n        onClick={testApiDiscovery}\n        disabled={isLoading}\n        style={{\n          padding: '10px 20px',\n          backgroundColor: isLoading ? '#666' : '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          marginRight: '10px'\n        }}\n      >\n        {isLoading ? '测试中...' : '测试API发现'}\n      </button>\n\n      {error && (\n        <div style={{\n          padding: '12px',\n          backgroundColor: '#ff4444',\n          color: '#fff',\n          margin: '8px 0',\n          borderRadius: '4px',\n          fontSize: '14px'\n        }}>\n          {error}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC/C,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGR,QAAQ,CAAC,IAAI,CAAC;EAC1C,MAAM,CAACS,SAAS,EAAEC,YAAY,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,aAAa,EAAEC,gBAAgB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMe,aAAa,GAAGd,MAAM,CAAC,IAAI,CAAC;EAElC,MAAMe,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFN,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMO,MAAM,GAAG,MAAMf,SAAS,CAAC,GAAG,CAAC;MACnCgB,KAAK,CAAC,YAAYD,MAAM,EAAE,CAAC;IAC7B,CAAC,CAAC,OAAON,KAAK,EAAE;MACdO,KAAK,CAAC,YAAYP,KAAK,CAACQ,OAAO,EAAE,CAAC;IACpC,CAAC,SAAS;MACRT,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEN,OAAA;IAAKgB,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACAnB,OAAA;MAAAmB,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBvB,OAAA;MAAAmB,QAAA,EAAG;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAEhBvB,OAAA;MAAKgB,KAAK,EAAE;QACVQ,eAAe,EAAE,SAAS;QAC1BP,OAAO,EAAE,MAAM;QACfQ,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,gBACAnB,OAAA;QAAAmB,QAAA,EAAI;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdvB,OAAA;QAAAmB,QAAA,gBACEnB,OAAA;UAAAmB,QAAA,GAAI,4BAAM,EAACd,SAAS,GAAG,GAAG,GAAG,GAAG;QAAA;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtCvB,OAAA;UAAAmB,QAAA,GAAI,4BAAM,EAACZ,KAAK,IAAI,GAAG;QAAA;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7BvB,OAAA;UAAAmB,QAAA,GAAI,gBAAI,EAAChB,MAAM,GAAG,KAAK,GAAG,KAAK;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAENvB,OAAA;MACE2B,OAAO,EAAEf,gBAAiB;MAC1BgB,QAAQ,EAAEvB,SAAU;MACpBW,KAAK,EAAE;QACLC,OAAO,EAAE,WAAW;QACpBO,eAAe,EAAEnB,SAAS,GAAG,MAAM,GAAG,SAAS;QAC/CwB,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdL,YAAY,EAAE,KAAK;QACnBM,MAAM,EAAE1B,SAAS,GAAG,aAAa,GAAG,SAAS;QAC7C2B,WAAW,EAAE;MACf,CAAE;MAAAb,QAAA,EAEDd,SAAS,GAAG,QAAQ,GAAG;IAAS;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,EAERhB,KAAK,iBACJP,OAAA;MAAKgB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfO,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,MAAM;QACbH,MAAM,EAAE,OAAO;QACfD,YAAY,EAAE,KAAK;QACnBQ,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,EACCZ;IAAK;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACrB,EAAA,CAvEQD,GAAG;AAAAiC,EAAA,GAAHjC,GAAG;AAyEZ,eAAeA,GAAG;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}