{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { getApiUrl } from './config/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadForm = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  onUpload,\n  isLoading\n}, ref) => {\n  _s();\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    const loadPresets = async () => {\n      try {\n        const apiUrl = await getApiUrl('/presets/');\n        const res = await fetch(apiUrl);\n        const data = await res.json();\n        if (data && data.presets) {\n          setPresets(data.presets);\n          if (data.presets.default) {\n            setParams(data.presets.default.params);\n          }\n        } else {\n          console.warn('预设配置格式不正确:', data);\n          // 设置默认的预设配置\n          setPresets({\n            default: {\n              params: params\n            },\n            portrait: {\n              params: {\n                ...params,\n                beauty: 0.3,\n                sharpening: 0.2\n              }\n            },\n            landscape: {\n              params: {\n                ...params,\n                saturation: 1.2,\n                contrast: 1.1\n              }\n            },\n            vintage: {\n              params: {\n                ...params,\n                saturation: 0.8,\n                contrast: 0.9\n              }\n            },\n            fast: {\n              params: {\n                ...params,\n                scale: 2\n              }\n            }\n          });\n        }\n      } catch (err) {\n        console.error('获取预设配置失败:', err);\n        // 设置默认的预设配置\n        setPresets({\n          default: {\n            params: params\n          },\n          portrait: {\n            params: {\n              ...params,\n              beauty: 0.3,\n              sharpening: 0.2\n            }\n          },\n          landscape: {\n            params: {\n              ...params,\n              saturation: 1.2,\n              contrast: 1.1\n            }\n          },\n          vintage: {\n            params: {\n              ...params,\n              saturation: 0.8,\n              contrast: 0.9\n            }\n          },\n          fast: {\n            params: {\n              ...params,\n              scale: 2\n            }\n          }\n        });\n      }\n    };\n    loadPresets();\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = presetKey => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n  const handleFileChange = e => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n      setSelectedFile(file);\n      setError(null);\n      const reader = new FileReader();\n      reader.onload = e => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  // 暴露给父组件的方法\n  useImperativeHandle(ref, () => ({\n    triggerSubmit: () => {\n      if (!selectedFile) {\n        setError('请选择要增强的图像');\n        return;\n      }\n      const formData = new FormData();\n      formData.append(\"file\", selectedFile);\n      formData.append(\"params\", JSON.stringify(params));\n      onUpload(formData);\n    }\n  }));\n  const SliderControl = ({\n    label,\n    value,\n    min,\n    max,\n    step,\n    onChange,\n    unit = ''\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      marginBottom: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '6px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          color: '#ddd',\n          fontSize: '13px',\n          fontWeight: '500'\n        },\n        children: label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: '#aaa',\n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        },\n        children: [typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value, unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"range\",\n      min: min,\n      max: max,\n      step: step,\n      value: value,\n      onChange: e => onChange(parseFloat(e.target.value)),\n      style: {\n        width: '100%',\n        height: '4px',\n        borderRadius: '2px',\n        background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${(value - min) / (max - min) * 100}%, #555 ${(value - min) / (max - min) * 100}%, #555 100%)`,\n        outline: 'none',\n        appearance: 'none',\n        cursor: 'pointer'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      style: {\n        flex: 1,\n        display: 'flex',\n        flexDirection: 'column'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #555'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '8px',\n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          },\n          children: \"\\u9009\\u62E9\\u56FE\\u50CF\\u6587\\u4EF6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          onChange: handleFileChange,\n          accept: \"image/*\",\n          required: true,\n          disabled: isLoading,\n          style: {\n            width: '100%',\n            padding: '8px',\n            backgroundColor: '#333',\n            border: '1px solid #555',\n            borderRadius: '4px',\n            color: '#ddd',\n            fontSize: '12px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), previewUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '12px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: previewUrl,\n            alt: \"\\u9884\\u89C8\",\n            style: {\n              maxWidth: '100%',\n              maxHeight: '120px',\n              borderRadius: '4px',\n              border: '1px solid #555'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            color: '#ff6b6b',\n            fontSize: '12px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '16px',\n          borderBottom: '1px solid #555'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            margin: '0 0 12px 0',\n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          },\n          children: \"\\u57FA\\u672C\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            style: {\n              display: 'block',\n              marginBottom: '6px',\n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            },\n            children: \"\\u9884\\u8BBE\\u914D\\u7F6E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: selectedPreset,\n            onChange: e => handlePresetChange(e.target.value),\n            style: {\n              width: '100%',\n              padding: '6px 8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"default\",\n              children: \"\\u9ED8\\u8BA4\\u8BBE\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"portrait\",\n              children: \"\\u4EBA\\u50CF\\u4F18\\u5316\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 275,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"landscape\",\n              children: \"\\u98CE\\u666F\\u589E\\u5F3A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"vintage\",\n              children: \"\\u590D\\u53E4\\u98CE\\u683C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"fast\",\n              children: \"\\u5FEB\\u901F\\u5904\\u7406\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"custom\",\n              children: \"\\u81EA\\u5B9A\\u4E49\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n          label: \"\\u8D85\\u5206\\u500D\\u6570\",\n          value: params.scale,\n          min: 2,\n          max: 4,\n          step: 2,\n          onChange: value => handleParamChange('scale', value),\n          unit: \"x\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          flex: 1,\n          overflowY: 'auto'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u9510\\u5316\\u548C\\u964D\\u566A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u9510\\u5316\",\n            value: params.sharpening,\n            min: 0,\n            max: 1,\n            step: 0.05,\n            onChange: value => handleParamChange('sharpening', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u964D\\u566A\",\n            value: params.denoising,\n            min: 0,\n            max: 30,\n            step: 1,\n            onChange: value => handleParamChange('denoising', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u8272\\u5F69\\u8C03\\u6574\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u9971\\u548C\\u5EA6\",\n            value: params.saturation,\n            min: 0,\n            max: 2,\n            step: 0.1,\n            onChange: value => handleParamChange('saturation', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u5BF9\\u6BD4\\u5EA6\",\n            value: params.contrast,\n            min: 0,\n            max: 2,\n            step: 0.05,\n            onChange: value => handleParamChange('contrast', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u4EAE\\u5EA6\",\n            value: params.brightness,\n            min: -100,\n            max: 100,\n            step: 5,\n            onChange: value => handleParamChange('brightness', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            },\n            children: \"\\u7F8E\\u989C\\u6548\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SliderControl, {\n            label: \"\\u7F8E\\u989C\\u5F3A\\u5EA6\",\n            value: params.beauty,\n            min: 0,\n            max: 1,\n            step: 0.05,\n            onChange: value => handleParamChange('beauty', value)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n}, \"ian5BaB6VzHYT9xqBGjrTyvip8A=\")), \"ian5BaB6VzHYT9xqBGjrTyvip8A=\");\n_c2 = UploadForm;\nexport default UploadForm;\nvar _c, _c2;\n$RefreshReg$(_c, \"UploadForm$forwardRef\");\n$RefreshReg$(_c2, \"UploadForm\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "forwardRef", "useImperativeHandle", "getApiUrl", "jsxDEV", "_jsxDEV", "UploadForm", "_s", "_c", "onUpload", "isLoading", "ref", "selectedFile", "setSelectedFile", "previewUrl", "setPreviewUrl", "error", "setError", "presets", "setPresets", "selectedP<PERSON>et", "setSelectedPreset", "params", "setParams", "scale", "sharpening", "denoising", "saturation", "contrast", "brightness", "beauty", "loadPresets", "apiUrl", "res", "fetch", "data", "json", "default", "console", "warn", "portrait", "landscape", "vintage", "fast", "err", "handlePresetChange", "preset<PERSON>ey", "handleParamChange", "key", "value", "prev", "handleFileChange", "e", "file", "target", "files", "size", "type", "startsWith", "reader", "FileReader", "onload", "result", "readAsDataURL", "handleSubmit", "preventDefault", "formData", "FormData", "append", "JSON", "stringify", "triggerSubmit", "SliderControl", "label", "min", "max", "step", "onChange", "unit", "style", "marginBottom", "children", "display", "justifyContent", "alignItems", "color", "fontSize", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "min<PERSON><PERSON><PERSON>", "textAlign", "toFixed", "parseFloat", "width", "height", "borderRadius", "background", "outline", "appearance", "cursor", "flexDirection", "onSubmit", "flex", "padding", "borderBottom", "accept", "required", "disabled", "backgroundColor", "border", "marginTop", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "margin", "overflowY", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/UploadForm.js"], "sourcesContent": ["import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';\nimport { getApiUrl } from './config/api';\n\nconst UploadForm = forwardRef(({ onUpload, isLoading }, ref) => {\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const [error, setError] = useState(null);\n  const [presets, setPresets] = useState({});\n  const [selectedPreset, setSelectedPreset] = useState('default');\n  const [params, setParams] = useState({\n    scale: 4,\n    sharpening: 0.0,\n    denoising: 0,\n    saturation: 1.0,\n    contrast: 1.0,\n    brightness: 0,\n    beauty: 0.0\n  });\n\n  // 获取预设配置\n  useEffect(() => {\n    const loadPresets = async () => {\n      try {\n        const apiUrl = await getApiUrl('/presets/');\n        const res = await fetch(apiUrl);\n        const data = await res.json();\n\n        if (data && data.presets) {\n          setPresets(data.presets);\n          if (data.presets.default) {\n            setParams(data.presets.default.params);\n          }\n        } else {\n          console.warn('预设配置格式不正确:', data);\n          // 设置默认的预设配置\n          setPresets({\n            default: { params: params },\n            portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },\n            landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },\n            vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },\n            fast: { params: { ...params, scale: 2 } }\n          });\n        }\n      } catch (err) {\n        console.error('获取预设配置失败:', err);\n        // 设置默认的预设配置\n        setPresets({\n          default: { params: params },\n          portrait: { params: { ...params, beauty: 0.3, sharpening: 0.2 } },\n          landscape: { params: { ...params, saturation: 1.2, contrast: 1.1 } },\n          vintage: { params: { ...params, saturation: 0.8, contrast: 0.9 } },\n          fast: { params: { ...params, scale: 2 } }\n        });\n      }\n    };\n\n    loadPresets();\n  }, []);\n\n  // 处理预设选择\n  const handlePresetChange = (presetKey) => {\n    setSelectedPreset(presetKey);\n    if (presets && presets[presetKey]) {\n      setParams(presets[presetKey].params);\n    }\n  };\n\n  // 处理参数变化\n  const handleParamChange = (key, value) => {\n    setParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n    setSelectedPreset('custom');\n  };\n\n  const handleFileChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      if (file.size > 10 * 1024 * 1024) {\n        setError('文件大小不能超过10MB');\n        return;\n      }\n\n      if (!file.type.startsWith('image/')) {\n        setError('请选择图像文件');\n        return;\n      }\n\n      setSelectedFile(file);\n      setError(null);\n\n      const reader = new FileReader();\n      reader.onload = (e) => setPreviewUrl(e.target.result);\n      reader.readAsDataURL(file);\n    } else {\n      setSelectedFile(null);\n      setPreviewUrl(null);\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n\n    if (!selectedFile) {\n      setError('请选择要增强的图像');\n      return;\n    }\n\n    const formData = new FormData();\n    formData.append(\"file\", selectedFile);\n    formData.append(\"params\", JSON.stringify(params));\n    onUpload(formData);\n  };\n\n  // 暴露给父组件的方法\n  useImperativeHandle(ref, () => ({\n    triggerSubmit: () => {\n      if (!selectedFile) {\n        setError('请选择要增强的图像');\n        return;\n      }\n\n      const formData = new FormData();\n      formData.append(\"file\", selectedFile);\n      formData.append(\"params\", JSON.stringify(params));\n      onUpload(formData);\n    }\n  }));\n\n  const SliderControl = ({ label, value, min, max, step, onChange, unit = '' }) => (\n    <div style={{ marginBottom: '16px' }}>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center',\n        marginBottom: '6px'\n      }}>\n        <label style={{ \n          color: '#ddd', \n          fontSize: '13px',\n          fontWeight: '500'\n        }}>\n          {label}\n        </label>\n        <span style={{ \n          color: '#aaa', \n          fontSize: '12px',\n          minWidth: '40px',\n          textAlign: 'right'\n        }}>\n          {typeof value === 'number' ? value.toFixed(step < 1 ? 1 : 0) : value}{unit}\n        </span>\n      </div>\n      <input \n        type=\"range\"\n        min={min}\n        max={max}\n        step={step}\n        value={value}\n        onChange={(e) => onChange(parseFloat(e.target.value))}\n        style={{\n          width: '100%',\n          height: '4px',\n          borderRadius: '2px',\n          background: `linear-gradient(to right, #4a90e2 0%, #4a90e2 ${((value - min) / (max - min)) * 100}%, #555 ${((value - min) / (max - min)) * 100}%, #555 100%)`,\n          outline: 'none',\n          appearance: 'none',\n          cursor: 'pointer'\n        }}\n      />\n    </div>\n  );\n\n  return (\n    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      <form onSubmit={handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>\n        \n        {/* 文件上传区域 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <label style={{ \n            display: 'block', \n            marginBottom: '8px', \n            color: '#ddd',\n            fontSize: '13px',\n            fontWeight: '500'\n          }}>\n            选择图像文件\n          </label>\n          <input\n            type=\"file\"\n            onChange={handleFileChange}\n            accept=\"image/*\"\n            required\n            disabled={isLoading}\n            style={{\n              width: '100%',\n              padding: '8px',\n              backgroundColor: '#333',\n              border: '1px solid #555',\n              borderRadius: '4px',\n              color: '#ddd',\n              fontSize: '12px'\n            }}\n          />\n          \n          {previewUrl && (\n            <div style={{ marginTop: '12px', textAlign: 'center' }}>\n              <img\n                src={previewUrl}\n                alt=\"预览\"\n                style={{\n                  maxWidth: '100%',\n                  maxHeight: '120px',\n                  borderRadius: '4px',\n                  border: '1px solid #555'\n                }}\n              />\n            </div>\n          )}\n          \n          {error && (\n            <div style={{ \n              marginTop: '8px',\n              color: '#ff6b6b', \n              fontSize: '12px'\n            }}>\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 基本设置 */}\n        <div style={{ \n          padding: '16px',\n          borderBottom: '1px solid #555'\n        }}>\n          <h3 style={{ \n            margin: '0 0 12px 0', \n            color: '#fff',\n            fontSize: '14px',\n            fontWeight: '600'\n          }}>\n            基本设置\n          </h3>\n          \n          {/* 预设选择 */}\n          <div style={{ marginBottom: '16px' }}>\n            <label style={{ \n              display: 'block', \n              marginBottom: '6px', \n              color: '#ddd',\n              fontSize: '13px',\n              fontWeight: '500'\n            }}>\n              预设配置\n            </label>\n            <select \n              value={selectedPreset}\n              onChange={(e) => handlePresetChange(e.target.value)}\n              style={{ \n                width: '100%', \n                padding: '6px 8px', \n                backgroundColor: '#333',\n                border: '1px solid #555',\n                borderRadius: '4px',\n                color: '#ddd',\n                fontSize: '12px'\n              }}\n            >\n              <option value=\"default\">默认设置</option>\n              <option value=\"portrait\">人像优化</option>\n              <option value=\"landscape\">风景增强</option>\n              <option value=\"vintage\">复古风格</option>\n              <option value=\"fast\">快速处理</option>\n              <option value=\"custom\">自定义</option>\n            </select>\n          </div>\n\n          {/* 超分倍数 */}\n          <SliderControl\n            label=\"超分倍数\"\n            value={params.scale}\n            min={2}\n            max={4}\n            step={2}\n            onChange={(value) => handleParamChange('scale', value)}\n            unit=\"x\"\n          />\n\n\n        </div>\n\n        {/* 高级调整 */}\n        <div style={{\n          flex: 1,\n          overflowY: 'auto'\n        }}>\n          {/* 锐化和降噪 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              锐化和降噪\n            </h3>\n\n            <SliderControl\n              label=\"锐化\"\n              value={params.sharpening}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('sharpening', value)}\n            />\n\n            <SliderControl\n              label=\"降噪\"\n              value={params.denoising}\n              min={0}\n              max={30}\n              step={1}\n              onChange={(value) => handleParamChange('denoising', value)}\n            />\n          </div>\n\n          {/* 色彩调整 */}\n          <div style={{\n            padding: '16px',\n            borderBottom: '1px solid #555'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              色彩调整\n            </h3>\n\n            <SliderControl\n              label=\"饱和度\"\n              value={params.saturation}\n              min={0}\n              max={2}\n              step={0.1}\n              onChange={(value) => handleParamChange('saturation', value)}\n            />\n\n            <SliderControl\n              label=\"对比度\"\n              value={params.contrast}\n              min={0}\n              max={2}\n              step={0.05}\n              onChange={(value) => handleParamChange('contrast', value)}\n            />\n\n            <SliderControl\n              label=\"亮度\"\n              value={params.brightness}\n              min={-100}\n              max={100}\n              step={5}\n              onChange={(value) => handleParamChange('brightness', value)}\n            />\n          </div>\n\n          {/* 美颜效果 */}\n          <div style={{\n            padding: '16px'\n          }}>\n            <h3 style={{\n              margin: '0 0 16px 0',\n              color: '#fff',\n              fontSize: '14px',\n              fontWeight: '600'\n            }}>\n              美颜效果\n            </h3>\n\n            <SliderControl\n              label=\"美颜强度\"\n              value={params.beauty}\n              min={0}\n              max={1}\n              step={0.05}\n              onChange={(value) => handleParamChange('beauty', value)}\n            />\n          </div>\n        </div>\n      </form>\n    </div>\n  );\n});\n\nexport default UploadForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,OAAO;AACnF,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,UAAU,gBAAAC,EAAA,cAAGN,UAAU,CAAAO,EAAA,GAAAD,EAAA,CAAC,CAAC;EAAEE,QAAQ;EAAEC;AAAU,CAAC,EAAEC,GAAG,KAAK;EAAAJ,EAAA;EAC9D,MAAM,CAACK,YAAY,EAAEC,eAAe,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACqB,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,QAAQ,CAAC,SAAS,CAAC;EAC/D,MAAM,CAACuB,MAAM,EAAEC,SAAS,CAAC,GAAGxB,QAAQ,CAAC;IACnCyB,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE,GAAG;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,GAAG;IACfC,QAAQ,EAAE,GAAG;IACbC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA9B,SAAS,CAAC,MAAM;IACd,MAAM+B,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACF,MAAMC,MAAM,GAAG,MAAM7B,SAAS,CAAC,WAAW,CAAC;QAC3C,MAAM8B,GAAG,GAAG,MAAMC,KAAK,CAACF,MAAM,CAAC;QAC/B,MAAMG,IAAI,GAAG,MAAMF,GAAG,CAACG,IAAI,CAAC,CAAC;QAE7B,IAAID,IAAI,IAAIA,IAAI,CAACjB,OAAO,EAAE;UACxBC,UAAU,CAACgB,IAAI,CAACjB,OAAO,CAAC;UACxB,IAAIiB,IAAI,CAACjB,OAAO,CAACmB,OAAO,EAAE;YACxBd,SAAS,CAACY,IAAI,CAACjB,OAAO,CAACmB,OAAO,CAACf,MAAM,CAAC;UACxC;QACF,CAAC,MAAM;UACLgB,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEJ,IAAI,CAAC;UAChC;UACAhB,UAAU,CAAC;YACTkB,OAAO,EAAE;cAAEf,MAAM,EAAEA;YAAO,CAAC;YAC3BkB,QAAQ,EAAE;cAAElB,MAAM,EAAE;gBAAE,GAAGA,MAAM;gBAAEQ,MAAM,EAAE,GAAG;gBAAEL,UAAU,EAAE;cAAI;YAAE,CAAC;YACjEgB,SAAS,EAAE;cAAEnB,MAAM,EAAE;gBAAE,GAAGA,MAAM;gBAAEK,UAAU,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAI;YAAE,CAAC;YACpEc,OAAO,EAAE;cAAEpB,MAAM,EAAE;gBAAE,GAAGA,MAAM;gBAAEK,UAAU,EAAE,GAAG;gBAAEC,QAAQ,EAAE;cAAI;YAAE,CAAC;YAClEe,IAAI,EAAE;cAAErB,MAAM,EAAE;gBAAE,GAAGA,MAAM;gBAAEE,KAAK,EAAE;cAAE;YAAE;UAC1C,CAAC,CAAC;QACJ;MACF,CAAC,CAAC,OAAOoB,GAAG,EAAE;QACZN,OAAO,CAACtB,KAAK,CAAC,WAAW,EAAE4B,GAAG,CAAC;QAC/B;QACAzB,UAAU,CAAC;UACTkB,OAAO,EAAE;YAAEf,MAAM,EAAEA;UAAO,CAAC;UAC3BkB,QAAQ,EAAE;YAAElB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEQ,MAAM,EAAE,GAAG;cAAEL,UAAU,EAAE;YAAI;UAAE,CAAC;UACjEgB,SAAS,EAAE;YAAEnB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEK,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAI;UAAE,CAAC;UACpEc,OAAO,EAAE;YAAEpB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEK,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAI;UAAE,CAAC;UAClEe,IAAI,EAAE;YAAErB,MAAM,EAAE;cAAE,GAAGA,MAAM;cAAEE,KAAK,EAAE;YAAE;UAAE;QAC1C,CAAC,CAAC;MACJ;IACF,CAAC;IAEDO,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMc,kBAAkB,GAAIC,SAAS,IAAK;IACxCzB,iBAAiB,CAACyB,SAAS,CAAC;IAC5B,IAAI5B,OAAO,IAAIA,OAAO,CAAC4B,SAAS,CAAC,EAAE;MACjCvB,SAAS,CAACL,OAAO,CAAC4B,SAAS,CAAC,CAACxB,MAAM,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMyB,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACxC1B,SAAS,CAAC2B,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,GAAG,GAAGC;IACT,CAAC,CAAC,CAAC;IACH5B,iBAAiB,CAAC,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAM8B,gBAAgB,GAAIC,CAAC,IAAK;IAC9B,MAAMC,IAAI,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAIF,IAAI,EAAE;MACR,IAAIA,IAAI,CAACG,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE;QAChCvC,QAAQ,CAAC,cAAc,CAAC;QACxB;MACF;MAEA,IAAI,CAACoC,IAAI,CAACI,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;QACnCzC,QAAQ,CAAC,SAAS,CAAC;QACnB;MACF;MAEAJ,eAAe,CAACwC,IAAI,CAAC;MACrBpC,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAM0C,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;MAC/BD,MAAM,CAACE,MAAM,GAAIT,CAAC,IAAKrC,aAAa,CAACqC,CAAC,CAACE,MAAM,CAACQ,MAAM,CAAC;MACrDH,MAAM,CAACI,aAAa,CAACV,IAAI,CAAC;IAC5B,CAAC,MAAM;MACLxC,eAAe,CAAC,IAAI,CAAC;MACrBE,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC;EAED,MAAMiD,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACrD,YAAY,EAAE;MACjBK,QAAQ,CAAC,WAAW,CAAC;MACrB;IACF;IAEA,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,YAAY,CAAC;IACrCsD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAChD,MAAM,CAAC,CAAC;IACjDb,QAAQ,CAACyD,QAAQ,CAAC;EACpB,CAAC;;EAED;EACAhE,mBAAmB,CAACS,GAAG,EAAE,OAAO;IAC9B4D,aAAa,EAAEA,CAAA,KAAM;MACnB,IAAI,CAAC3D,YAAY,EAAE;QACjBK,QAAQ,CAAC,WAAW,CAAC;QACrB;MACF;MAEA,MAAMiD,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExD,YAAY,CAAC;MACrCsD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAChD,MAAM,CAAC,CAAC;MACjDb,QAAQ,CAACyD,QAAQ,CAAC;IACpB;EACF,CAAC,CAAC,CAAC;EAEH,MAAMM,aAAa,GAAGA,CAAC;IAAEC,KAAK;IAAExB,KAAK;IAAEyB,GAAG;IAAEC,GAAG;IAAEC,IAAI;IAAEC,QAAQ;IAAEC,IAAI,GAAG;EAAG,CAAC,kBAC1EzE,OAAA;IAAK0E,KAAK,EAAE;MAAEC,YAAY,EAAE;IAAO,CAAE;IAAAC,QAAA,gBACnC5E,OAAA;MAAK0E,KAAK,EAAE;QACVG,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBJ,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,gBACA5E,OAAA;QAAO0E,KAAK,EAAE;UACZM,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE;QACd,CAAE;QAAAN,QAAA,EACCR;MAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACRtF,OAAA;QAAM0E,KAAK,EAAE;UACXM,KAAK,EAAE,MAAM;UACbC,QAAQ,EAAE,MAAM;UAChBM,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAE;QAAAZ,QAAA,GACC,OAAOhC,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAAC6C,OAAO,CAAClB,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG3B,KAAK,EAAE6B,IAAI;MAAA;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACNtF,OAAA;MACEoD,IAAI,EAAC,OAAO;MACZiB,GAAG,EAAEA,GAAI;MACTC,GAAG,EAAEA,GAAI;MACTC,IAAI,EAAEA,IAAK;MACX3B,KAAK,EAAEA,KAAM;MACb4B,QAAQ,EAAGzB,CAAC,IAAKyB,QAAQ,CAACkB,UAAU,CAAC3C,CAAC,CAACE,MAAM,CAACL,KAAK,CAAC,CAAE;MACtD8B,KAAK,EAAE;QACLiB,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,KAAK;QACbC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,iDAAkD,CAAClD,KAAK,GAAGyB,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,WAAY,CAACzB,KAAK,GAAGyB,GAAG,KAAKC,GAAG,GAAGD,GAAG,CAAC,GAAI,GAAG,eAAe;QAC7J0B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,MAAM;QAClBC,MAAM,EAAE;MACV;IAAE;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CACN;EAED,oBACEtF,OAAA;IAAK0E,KAAK,EAAE;MAAEkB,MAAM,EAAE,MAAM;MAAEf,OAAO,EAAE,MAAM;MAAEqB,aAAa,EAAE;IAAS,CAAE;IAAAtB,QAAA,eACvE5E,OAAA;MAAMmG,QAAQ,EAAExC,YAAa;MAACe,KAAK,EAAE;QAAE0B,IAAI,EAAE,CAAC;QAAEvB,OAAO,EAAE,MAAM;QAAEqB,aAAa,EAAE;MAAS,CAAE;MAAAtB,QAAA,gBAGzF5E,OAAA;QAAK0E,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA1B,QAAA,gBACA5E,OAAA;UAAO0E,KAAK,EAAE;YACZG,OAAO,EAAE,OAAO;YAChBF,YAAY,EAAE,KAAK;YACnBK,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtF,OAAA;UACEoD,IAAI,EAAC,MAAM;UACXoB,QAAQ,EAAE1B,gBAAiB;UAC3ByD,MAAM,EAAC,SAAS;UAChBC,QAAQ;UACRC,QAAQ,EAAEpG,SAAU;UACpBqE,KAAK,EAAE;YACLiB,KAAK,EAAE,MAAM;YACbU,OAAO,EAAE,KAAK;YACdK,eAAe,EAAE,MAAM;YACvBC,MAAM,EAAE,gBAAgB;YACxBd,YAAY,EAAE,KAAK;YACnBb,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE;UACZ;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAED7E,UAAU,iBACTT,OAAA;UAAK0E,KAAK,EAAE;YAAEkC,SAAS,EAAE,MAAM;YAAEpB,SAAS,EAAE;UAAS,CAAE;UAAAZ,QAAA,eACrD5E,OAAA;YACE6G,GAAG,EAAEpG,UAAW;YAChBqG,GAAG,EAAC,cAAI;YACRpC,KAAK,EAAE;cACLqC,QAAQ,EAAE,MAAM;cAChBC,SAAS,EAAE,OAAO;cAClBnB,YAAY,EAAE,KAAK;cACnBc,MAAM,EAAE;YACV;UAAE;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEA3E,KAAK,iBACJX,OAAA;UAAK0E,KAAK,EAAE;YACVkC,SAAS,EAAE,KAAK;YAChB5B,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,EACCjE;QAAK;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNtF,OAAA;QAAK0E,KAAK,EAAE;UACV2B,OAAO,EAAE,MAAM;UACfC,YAAY,EAAE;QAChB,CAAE;QAAA1B,QAAA,gBACA5E,OAAA;UAAI0E,KAAK,EAAE;YACTuC,MAAM,EAAE,YAAY;YACpBjC,KAAK,EAAE,MAAM;YACbC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAAN,QAAA,EAAC;QAEH;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAGLtF,OAAA;UAAK0E,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAAAC,QAAA,gBACnC5E,OAAA;YAAO0E,KAAK,EAAE;cACZG,OAAO,EAAE,OAAO;cAChBF,YAAY,EAAE,KAAK;cACnBK,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACRtF,OAAA;YACE4C,KAAK,EAAE7B,cAAe;YACtByD,QAAQ,EAAGzB,CAAC,IAAKP,kBAAkB,CAACO,CAAC,CAACE,MAAM,CAACL,KAAK,CAAE;YACpD8B,KAAK,EAAE;cACLiB,KAAK,EAAE,MAAM;cACbU,OAAO,EAAE,SAAS;cAClBK,eAAe,EAAE,MAAM;cACvBC,MAAM,EAAE,gBAAgB;cACxBd,YAAY,EAAE,KAAK;cACnBb,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE;YACZ,CAAE;YAAAL,QAAA,gBAEF5E,OAAA;cAAQ4C,KAAK,EAAC,SAAS;cAAAgC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCtF,OAAA;cAAQ4C,KAAK,EAAC,UAAU;cAAAgC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCtF,OAAA;cAAQ4C,KAAK,EAAC,WAAW;cAAAgC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACvCtF,OAAA;cAAQ4C,KAAK,EAAC,SAAS;cAAAgC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCtF,OAAA;cAAQ4C,KAAK,EAAC,MAAM;cAAAgC,QAAA,EAAC;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClCtF,OAAA;cAAQ4C,KAAK,EAAC,QAAQ;cAAAgC,QAAA,EAAC;YAAG;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtF,OAAA,CAACmE,aAAa;UACZC,KAAK,EAAC,0BAAM;UACZxB,KAAK,EAAE3B,MAAM,CAACE,KAAM;UACpBkD,GAAG,EAAE,CAAE;UACPC,GAAG,EAAE,CAAE;UACPC,IAAI,EAAE,CAAE;UACRC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,OAAO,EAAEE,KAAK,CAAE;UACvD6B,IAAI,EAAC;QAAG;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGC,CAAC,eAGNtF,OAAA;QAAK0E,KAAK,EAAE;UACV0B,IAAI,EAAE,CAAC;UACPc,SAAS,EAAE;QACb,CAAE;QAAAtC,QAAA,gBAEA5E,OAAA;UAAK0E,KAAK,EAAE;YACV2B,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,gBACA5E,OAAA;YAAI0E,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELtF,OAAA,CAACmE,aAAa;YACZC,KAAK,EAAC,cAAI;YACVxB,KAAK,EAAE3B,MAAM,CAACG,UAAW;YACzBiD,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAEFtF,OAAA,CAACmE,aAAa;YACZC,KAAK,EAAC,cAAI;YACVxB,KAAK,EAAE3B,MAAM,CAACI,SAAU;YACxBgD,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,EAAG;YACRC,IAAI,EAAE,CAAE;YACRC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,WAAW,EAAEE,KAAK;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAK0E,KAAK,EAAE;YACV2B,OAAO,EAAE,MAAM;YACfC,YAAY,EAAE;UAChB,CAAE;UAAA1B,QAAA,gBACA5E,OAAA;YAAI0E,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELtF,OAAA,CAACmE,aAAa;YACZC,KAAK,EAAC,oBAAK;YACXxB,KAAK,EAAE3B,MAAM,CAACK,UAAW;YACzB+C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,GAAI;YACVC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eAEFtF,OAAA,CAACmE,aAAa;YACZC,KAAK,EAAC,oBAAK;YACXxB,KAAK,EAAE3B,MAAM,CAACM,QAAS;YACvB8C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,UAAU,EAAEE,KAAK;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eAEFtF,OAAA,CAACmE,aAAa;YACZC,KAAK,EAAC,cAAI;YACVxB,KAAK,EAAE3B,MAAM,CAACO,UAAW;YACzB6C,GAAG,EAAE,CAAC,GAAI;YACVC,GAAG,EAAE,GAAI;YACTC,IAAI,EAAE,CAAE;YACRC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,YAAY,EAAEE,KAAK;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNtF,OAAA;UAAK0E,KAAK,EAAE;YACV2B,OAAO,EAAE;UACX,CAAE;UAAAzB,QAAA,gBACA5E,OAAA;YAAI0E,KAAK,EAAE;cACTuC,MAAM,EAAE,YAAY;cACpBjC,KAAK,EAAE,MAAM;cACbC,QAAQ,EAAE,MAAM;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAN,QAAA,EAAC;UAEH;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELtF,OAAA,CAACmE,aAAa;YACZC,KAAK,EAAC,0BAAM;YACZxB,KAAK,EAAE3B,MAAM,CAACQ,MAAO;YACrB4C,GAAG,EAAE,CAAE;YACPC,GAAG,EAAE,CAAE;YACPC,IAAI,EAAE,IAAK;YACXC,QAAQ,EAAG5B,KAAK,IAAKF,iBAAiB,CAAC,QAAQ,EAAEE,KAAK;UAAE;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC,kCAAC;AAAC6B,GAAA,GA/YGlH,UAAU;AAiZhB,eAAeA,UAAU;AAAC,IAAAE,EAAA,EAAAgH,GAAA;AAAAC,YAAA,CAAAjH,EAAA;AAAAiH,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}