{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { getApiUrl } from './config/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  var _apiResult$response;\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [apiResult, setApiResult] = useState(null);\n  const testApiDiscovery = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      console.log('开始API发现测试...');\n      const apiUrl = await getApiUrl('/');\n      console.log('发现的API地址:', apiUrl);\n\n      // 测试API响应\n      const response = await fetch(apiUrl);\n      const data = await response.json();\n      setApiResult({\n        url: apiUrl,\n        response: data\n      });\n      alert(`API发现成功!\\n地址: ${apiUrl}\\n响应: ${data.message}`);\n    } catch (error) {\n      console.error('API发现失败:', error);\n      setError(error.message);\n      alert(`API发现失败: ${error.message}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\u56FE\\u50CF\\u589E\\u5F3A\\u5E94\\u7528 - API\\u6D4B\\u8BD5\\u7248\\u672C\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\u6D4B\\u8BD5API\\u81EA\\u52A8\\u53D1\\u73B0\\u529F\\u80FD\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        backgroundColor: '#f0f0f0',\n        padding: '15px',\n        borderRadius: '5px',\n        margin: '20px 0'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\u72B6\\u6001\\u4FE1\\u606F:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u52A0\\u8F7D\\u72B6\\u6001: \", isLoading ? '测试中...' : '空闲']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"\\u9519\\u8BEF\\u4FE1\\u606F: \", error || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"API\\u5730\\u5740: \", (apiResult === null || apiResult === void 0 ? void 0 : apiResult.url) || '未发现']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: [\"API\\u54CD\\u5E94: \", (apiResult === null || apiResult === void 0 ? void 0 : (_apiResult$response = apiResult.response) === null || _apiResult$response === void 0 ? void 0 : _apiResult$response.message) || '无']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: testApiDiscovery,\n      disabled: isLoading,\n      style: {\n        padding: '10px 20px',\n        backgroundColor: isLoading ? '#666' : '#007bff',\n        color: 'white',\n        border: 'none',\n        borderRadius: '4px',\n        cursor: isLoading ? 'not-allowed' : 'pointer',\n        marginRight: '10px'\n      },\n      children: isLoading ? '测试中...' : '测试API发现'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '12px',\n        backgroundColor: '#ff4444',\n        color: '#fff',\n        margin: '8px 0',\n        borderRadius: '4px',\n        fontSize: '14px'\n      },\n      children: [\"\\u9519\\u8BEF: \", error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this), apiResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '12px',\n        backgroundColor: '#44ff44',\n        color: '#000',\n        margin: '8px 0',\n        borderRadius: '4px',\n        fontSize: '14px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"API\\u53D1\\u73B0\\u6210\\u529F!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 36\n      }, this), \"\\u5730\\u5740: \", apiResult.url, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 30\n      }, this), \"\\u54CD\\u5E94: \", JSON.stringify(apiResult.response, null, 2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"BgmLeRs/Zo8LAFKhpKN6X+Hwrps=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "getApiUrl", "jsxDEV", "_jsxDEV", "App", "_s", "_apiResult$response", "isLoading", "setIsLoading", "error", "setError", "apiResult", "setApiResult", "testApiDiscovery", "console", "log", "apiUrl", "response", "fetch", "data", "json", "url", "alert", "message", "style", "padding", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "backgroundColor", "borderRadius", "margin", "onClick", "disabled", "color", "border", "cursor", "marginRight", "fontSize", "JSON", "stringify", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { getApiUrl } from './config/api';\n\nfunction App() {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [apiResult, setApiResult] = useState(null);\n\n  const testApiDiscovery = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      console.log('开始API发现测试...');\n      \n      const apiUrl = await getApiUrl('/');\n      console.log('发现的API地址:', apiUrl);\n      \n      // 测试API响应\n      const response = await fetch(apiUrl);\n      const data = await response.json();\n      \n      setApiResult({\n        url: apiUrl,\n        response: data\n      });\n      \n      alert(`API发现成功!\\n地址: ${apiUrl}\\n响应: ${data.message}`);\n    } catch (error) {\n      console.error('API发现失败:', error);\n      setError(error.message);\n      alert(`API发现失败: ${error.message}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ \n      padding: '20px',\n      fontFamily: 'Arial, sans-serif'\n    }}>\n      <h1>图像增强应用 - API测试版本</h1>\n      <p>测试API自动发现功能</p>\n      \n      <div style={{\n        backgroundColor: '#f0f0f0',\n        padding: '15px',\n        borderRadius: '5px',\n        margin: '20px 0'\n      }}>\n        <h3>状态信息:</h3>\n        <ul>\n          <li>加载状态: {isLoading ? '测试中...' : '空闲'}</li>\n          <li>错误信息: {error || '无'}</li>\n          <li>API地址: {apiResult?.url || '未发现'}</li>\n          <li>API响应: {apiResult?.response?.message || '无'}</li>\n        </ul>\n      </div>\n      \n      <button \n        onClick={testApiDiscovery}\n        disabled={isLoading}\n        style={{\n          padding: '10px 20px',\n          backgroundColor: isLoading ? '#666' : '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: isLoading ? 'not-allowed' : 'pointer',\n          marginRight: '10px'\n        }}\n      >\n        {isLoading ? '测试中...' : '测试API发现'}\n      </button>\n      \n      {error && (\n        <div style={{\n          padding: '12px',\n          backgroundColor: '#ff4444',\n          color: '#fff',\n          margin: '8px 0',\n          borderRadius: '4px',\n          fontSize: '14px'\n        }}>\n          错误: {error}\n        </div>\n      )}\n      \n      {apiResult && (\n        <div style={{\n          padding: '12px',\n          backgroundColor: '#44ff44',\n          color: '#000',\n          margin: '8px 0',\n          borderRadius: '4px',\n          fontSize: '14px'\n        }}>\n          <strong>API发现成功!</strong><br/>\n          地址: {apiResult.url}<br/>\n          响应: {JSON.stringify(apiResult.response, null, 2)}\n        </div>\n      )}\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,mBAAA;EACb,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAMa,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFL,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MACdI,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;MAE3B,MAAMC,MAAM,GAAG,MAAMf,SAAS,CAAC,GAAG,CAAC;MACnCa,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,MAAM,CAAC;;MAEhC;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACF,MAAM,CAAC;MACpC,MAAMG,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAElCR,YAAY,CAAC;QACXS,GAAG,EAAEL,MAAM;QACXC,QAAQ,EAAEE;MACZ,CAAC,CAAC;MAEFG,KAAK,CAAC,iBAAiBN,MAAM,SAASG,IAAI,CAACI,OAAO,EAAE,CAAC;IACvD,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChCC,QAAQ,CAACD,KAAK,CAACc,OAAO,CAAC;MACvBD,KAAK,CAAC,YAAYb,KAAK,CAACc,OAAO,EAAE,CAAC;IACpC,CAAC,SAAS;MACRf,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEL,OAAA;IAAKqB,KAAK,EAAE;MACVC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE;IACd,CAAE;IAAAC,QAAA,gBACAxB,OAAA;MAAAwB,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzB5B,OAAA;MAAAwB,QAAA,EAAG;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAElB5B,OAAA;MAAKqB,KAAK,EAAE;QACVQ,eAAe,EAAE,SAAS;QAC1BP,OAAO,EAAE,MAAM;QACfQ,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE;MACV,CAAE;MAAAP,QAAA,gBACAxB,OAAA;QAAAwB,QAAA,EAAI;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACd5B,OAAA;QAAAwB,QAAA,gBACExB,OAAA;UAAAwB,QAAA,GAAI,4BAAM,EAACpB,SAAS,GAAG,QAAQ,GAAG,IAAI;QAAA;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5C5B,OAAA;UAAAwB,QAAA,GAAI,4BAAM,EAAClB,KAAK,IAAI,GAAG;QAAA;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B5B,OAAA;UAAAwB,QAAA,GAAI,mBAAO,EAAC,CAAAhB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEU,GAAG,KAAI,KAAK;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzC5B,OAAA;UAAAwB,QAAA,GAAI,mBAAO,EAAC,CAAAhB,SAAS,aAATA,SAAS,wBAAAL,mBAAA,GAATK,SAAS,CAAEM,QAAQ,cAAAX,mBAAA,uBAAnBA,mBAAA,CAAqBiB,OAAO,KAAI,GAAG;QAAA;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEN5B,OAAA;MACEgC,OAAO,EAAEtB,gBAAiB;MAC1BuB,QAAQ,EAAE7B,SAAU;MACpBiB,KAAK,EAAE;QACLC,OAAO,EAAE,WAAW;QACpBO,eAAe,EAAEzB,SAAS,GAAG,MAAM,GAAG,SAAS;QAC/C8B,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,MAAM;QACdL,YAAY,EAAE,KAAK;QACnBM,MAAM,EAAEhC,SAAS,GAAG,aAAa,GAAG,SAAS;QAC7CiC,WAAW,EAAE;MACf,CAAE;MAAAb,QAAA,EAEDpB,SAAS,GAAG,QAAQ,GAAG;IAAS;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,EAERtB,KAAK,iBACJN,OAAA;MAAKqB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfO,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,MAAM;QACbH,MAAM,EAAE,OAAO;QACfD,YAAY,EAAE,KAAK;QACnBQ,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,GAAC,gBACG,EAAClB,KAAK;IAAA;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACN,EAEApB,SAAS,iBACRR,OAAA;MAAKqB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfO,eAAe,EAAE,SAAS;QAC1BK,KAAK,EAAE,MAAM;QACbH,MAAM,EAAE,OAAO;QACfD,YAAY,EAAE,KAAK;QACnBQ,QAAQ,EAAE;MACZ,CAAE;MAAAd,QAAA,gBACAxB,OAAA;QAAAwB,QAAA,EAAQ;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAAA5B,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,kBAC1B,EAACpB,SAAS,CAACU,GAAG,eAAClB,OAAA;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,kBACpB,EAACW,IAAI,CAACC,SAAS,CAAChC,SAAS,CAACM,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC1B,EAAA,CArGQD,GAAG;AAAAwC,EAAA,GAAHxC,GAAG;AAuGZ,eAAeA,GAAG;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}