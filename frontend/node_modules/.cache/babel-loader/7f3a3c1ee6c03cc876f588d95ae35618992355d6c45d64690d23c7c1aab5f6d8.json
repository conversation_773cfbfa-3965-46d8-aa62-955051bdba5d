{"ast": null, "code": "/**\n * API配置管理模块\n * 实现前端自动发现后端服务地址的机制\n */class ApiConfig{constructor(){this.baseUrl=null;this.isDiscovering=false;this.discoveryPromise=null;}/**\n   * 自动发现后端服务地址\n   * @returns {Promise<string>} 后端服务的基础URL\n   */async discoverBackendUrl(){if(this.baseUrl){return this.baseUrl;}if(this.isDiscovering){return this.discoveryPromise;}this.isDiscovering=true;this.discoveryPromise=this._performDiscovery();try{this.baseUrl=await this.discoveryPromise;return this.baseUrl;}finally{this.isDiscovering=false;}}/**\n   * 执行服务发现逻辑\n   * @private\n   */async _performDiscovery(){// 候选的后端服务地址列表\nconst candidates=this._getCandidateUrls();console.log('🔍 开始自动发现后端服务...',candidates);// 并行测试所有候选地址\nconst testPromises=candidates.map(url=>this._testBackendUrl(url));try{// 使用Promise.allSettled等待所有测试完成\nconst results=await Promise.allSettled(testPromises);// 找到第一个成功的地址\nfor(let i=0;i<results.length;i++){if(results[i].status==='fulfilled'&&results[i].value){const discoveredUrl=candidates[i];console.log('✅ 发现后端服务:',discoveredUrl);return discoveredUrl;}}// 如果所有地址都失败，抛出错误\nthrow new Error('无法发现后端服务，请确保后端服务正在运行');}catch(error){console.error('❌ 后端服务发现失败:',error);throw error;}}/**\n   * 获取候选的后端服务地址\n   * @private\n   */_getCandidateUrls(){const currentHost=window.location.hostname;const currentProtocol=window.location.protocol;// 常见的后端端口\nconst commonPorts=[8001,8000,5000,3001,8080];const candidates=[];// 1. 环境变量配置的地址（优先级最高）\nif(process.env.REACT_APP_API_URL){candidates.push(process.env.REACT_APP_API_URL);}// 2. 同主机的常见端口\nfor(const port of commonPorts){candidates.push(`${currentProtocol}//${currentHost}:${port}`);}// 3. localhost的常见端口（如果当前不是localhost）\nif(currentHost!=='localhost'&&currentHost!=='127.0.0.1'){for(const port of commonPorts){candidates.push(`${currentProtocol}//localhost:${port}`);candidates.push(`${currentProtocol}//127.0.0.1:${port}`);}}// 去重\nreturn[...new Set(candidates)];}/**\n   * 测试后端服务地址是否可用\n   * @private\n   */async _testBackendUrl(url){try{const controller=new AbortController();const timeoutId=setTimeout(()=>controller.abort(),3000);// 3秒超时\nconst response=await fetch(`${url}/`,{method:'GET',signal:controller.signal,headers:{'Accept':'application/json'}});clearTimeout(timeoutId);if(response.ok){const data=await response.json();// 验证响应是否包含预期的字段\nif(data&&data.message&&data.message.includes('图像增强')){return true;}}return false;}catch(error){// 网络错误、超时等\nreturn false;}}/**\n   * 获取完整的API URL\n   */async getApiUrl(){let endpoint=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'';const baseUrl=await this.discoverBackendUrl();return`${baseUrl}${endpoint}`;}/**\n   * 重置配置，强制重新发现\n   */reset(){this.baseUrl=null;this.isDiscovering=false;this.discoveryPromise=null;}/**\n   * 手动设置后端地址（用于测试或特殊情况）\n   */setBackendUrl(url){this.baseUrl=url;}}// 创建全局实例\nconst apiConfig=new ApiConfig();export default apiConfig;// 便捷方法\nexport const getApiUrl=function(){let endpoint=arguments.length>0&&arguments[0]!==undefined?arguments[0]:'';return apiConfig.getApiUrl(endpoint);};export const resetApiConfig=()=>apiConfig.reset();export const setBackendUrl=url=>apiConfig.setBackendUrl(url);", "map": {"version": 3, "names": ["ApiConfig", "constructor", "baseUrl", "isDiscovering", "discoveryPromise", "discoverBackendUrl", "_performDiscovery", "candidates", "_getCandidateUrls", "console", "log", "testPromises", "map", "url", "_testBackendUrl", "results", "Promise", "allSettled", "i", "length", "status", "value", "discoveredUrl", "Error", "error", "currentHost", "window", "location", "hostname", "currentProtocol", "protocol", "commonPorts", "process", "env", "REACT_APP_API_URL", "push", "port", "Set", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "method", "signal", "headers", "clearTimeout", "ok", "data", "json", "message", "includes", "getApiUrl", "endpoint", "arguments", "undefined", "reset", "setBackendUrl", "apiConfig", "resetApiConfig"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/config/api.js"], "sourcesContent": ["/**\n * API配置管理模块\n * 实现前端自动发现后端服务地址的机制\n */\n\nclass ApiConfig {\n  constructor() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  /**\n   * 自动发现后端服务地址\n   * @returns {Promise<string>} 后端服务的基础URL\n   */\n  async discoverBackendUrl() {\n    if (this.baseUrl) {\n      return this.baseUrl;\n    }\n\n    if (this.isDiscovering) {\n      return this.discoveryPromise;\n    }\n\n    this.isDiscovering = true;\n    this.discoveryPromise = this._performDiscovery();\n\n    try {\n      this.baseUrl = await this.discoveryPromise;\n      return this.baseUrl;\n    } finally {\n      this.isDiscovering = false;\n    }\n  }\n\n  /**\n   * 执行服务发现逻辑\n   * @private\n   */\n  async _performDiscovery() {\n    // 候选的后端服务地址列表\n    const candidates = this._getCandidateUrls();\n    \n    console.log('🔍 开始自动发现后端服务...', candidates);\n\n    // 并行测试所有候选地址\n    const testPromises = candidates.map(url => this._testBackendUrl(url));\n    \n    try {\n      // 使用Promise.allSettled等待所有测试完成\n      const results = await Promise.allSettled(testPromises);\n      \n      // 找到第一个成功的地址\n      for (let i = 0; i < results.length; i++) {\n        if (results[i].status === 'fulfilled' && results[i].value) {\n          const discoveredUrl = candidates[i];\n          console.log('✅ 发现后端服务:', discoveredUrl);\n          return discoveredUrl;\n        }\n      }\n      \n      // 如果所有地址都失败，抛出错误\n      throw new Error('无法发现后端服务，请确保后端服务正在运行');\n      \n    } catch (error) {\n      console.error('❌ 后端服务发现失败:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 获取候选的后端服务地址\n   * @private\n   */\n  _getCandidateUrls() {\n    const currentHost = window.location.hostname;\n    const currentProtocol = window.location.protocol;\n    \n    // 常见的后端端口\n    const commonPorts = [8001, 8000, 5000, 3001, 8080];\n    \n    const candidates = [];\n    \n    // 1. 环境变量配置的地址（优先级最高）\n    if (process.env.REACT_APP_API_URL) {\n      candidates.push(process.env.REACT_APP_API_URL);\n    }\n    \n    // 2. 同主机的常见端口\n    for (const port of commonPorts) {\n      candidates.push(`${currentProtocol}//${currentHost}:${port}`);\n    }\n    \n    // 3. localhost的常见端口（如果当前不是localhost）\n    if (currentHost !== 'localhost' && currentHost !== '127.0.0.1') {\n      for (const port of commonPorts) {\n        candidates.push(`${currentProtocol}//localhost:${port}`);\n        candidates.push(`${currentProtocol}//127.0.0.1:${port}`);\n      }\n    }\n    \n    // 去重\n    return [...new Set(candidates)];\n  }\n\n  /**\n   * 测试后端服务地址是否可用\n   * @private\n   */\n  async _testBackendUrl(url) {\n    try {\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时\n      \n      const response = await fetch(`${url}/`, {\n        method: 'GET',\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json',\n        }\n      });\n      \n      clearTimeout(timeoutId);\n      \n      if (response.ok) {\n        const data = await response.json();\n        // 验证响应是否包含预期的字段\n        if (data && data.message && data.message.includes('图像增强')) {\n          return true;\n        }\n      }\n      \n      return false;\n    } catch (error) {\n      // 网络错误、超时等\n      return false;\n    }\n  }\n\n  /**\n   * 获取完整的API URL\n   */\n  async getApiUrl(endpoint = '') {\n    const baseUrl = await this.discoverBackendUrl();\n    return `${baseUrl}${endpoint}`;\n  }\n\n  /**\n   * 重置配置，强制重新发现\n   */\n  reset() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  /**\n   * 手动设置后端地址（用于测试或特殊情况）\n   */\n  setBackendUrl(url) {\n    this.baseUrl = url;\n  }\n}\n\n// 创建全局实例\nconst apiConfig = new ApiConfig();\n\nexport default apiConfig;\n\n// 便捷方法\nexport const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);\nexport const resetApiConfig = () => apiConfig.reset();\nexport const setBackendUrl = (url) => apiConfig.setBackendUrl(url);\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,KAAM,CAAAA,SAAU,CACdC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAG,IAAI,CACnB,IAAI,CAACC,aAAa,CAAG,KAAK,CAC1B,IAAI,CAACC,gBAAgB,CAAG,IAAI,CAC9B,CAEA;AACF;AACA;AACA,KACE,KAAM,CAAAC,kBAAkBA,CAAA,CAAG,CACzB,GAAI,IAAI,CAACH,OAAO,CAAE,CAChB,MAAO,KAAI,CAACA,OAAO,CACrB,CAEA,GAAI,IAAI,CAACC,aAAa,CAAE,CACtB,MAAO,KAAI,CAACC,gBAAgB,CAC9B,CAEA,IAAI,CAACD,aAAa,CAAG,IAAI,CACzB,IAAI,CAACC,gBAAgB,CAAG,IAAI,CAACE,iBAAiB,CAAC,CAAC,CAEhD,GAAI,CACF,IAAI,CAACJ,OAAO,CAAG,KAAM,KAAI,CAACE,gBAAgB,CAC1C,MAAO,KAAI,CAACF,OAAO,CACrB,CAAC,OAAS,CACR,IAAI,CAACC,aAAa,CAAG,KAAK,CAC5B,CACF,CAEA;AACF;AACA;AACA,KACE,KAAM,CAAAG,iBAAiBA,CAAA,CAAG,CACxB;AACA,KAAM,CAAAC,UAAU,CAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC,CAE3CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAEH,UAAU,CAAC,CAE3C;AACA,KAAM,CAAAI,YAAY,CAAGJ,UAAU,CAACK,GAAG,CAACC,GAAG,EAAI,IAAI,CAACC,eAAe,CAACD,GAAG,CAAC,CAAC,CAErE,GAAI,CACF;AACA,KAAM,CAAAE,OAAO,CAAG,KAAM,CAAAC,OAAO,CAACC,UAAU,CAACN,YAAY,CAAC,CAEtD;AACA,IAAK,GAAI,CAAAO,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGH,OAAO,CAACI,MAAM,CAAED,CAAC,EAAE,CAAE,CACvC,GAAIH,OAAO,CAACG,CAAC,CAAC,CAACE,MAAM,GAAK,WAAW,EAAIL,OAAO,CAACG,CAAC,CAAC,CAACG,KAAK,CAAE,CACzD,KAAM,CAAAC,aAAa,CAAGf,UAAU,CAACW,CAAC,CAAC,CACnCT,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEY,aAAa,CAAC,CACvC,MAAO,CAAAA,aAAa,CACtB,CACF,CAEA;AACA,KAAM,IAAI,CAAAC,KAAK,CAAC,sBAAsB,CAAC,CAEzC,CAAE,MAAOC,KAAK,CAAE,CACdf,OAAO,CAACe,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA,KACEhB,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAiB,WAAW,CAAGC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAC5C,KAAM,CAAAC,eAAe,CAAGH,MAAM,CAACC,QAAQ,CAACG,QAAQ,CAEhD;AACA,KAAM,CAAAC,WAAW,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAElD,KAAM,CAAAxB,UAAU,CAAG,EAAE,CAErB;AACA,GAAIyB,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAE,CACjC3B,UAAU,CAAC4B,IAAI,CAACH,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC,CAChD,CAEA;AACA,IAAK,KAAM,CAAAE,IAAI,GAAI,CAAAL,WAAW,CAAE,CAC9BxB,UAAU,CAAC4B,IAAI,CAAC,GAAGN,eAAe,KAAKJ,WAAW,IAAIW,IAAI,EAAE,CAAC,CAC/D,CAEA;AACA,GAAIX,WAAW,GAAK,WAAW,EAAIA,WAAW,GAAK,WAAW,CAAE,CAC9D,IAAK,KAAM,CAAAW,IAAI,GAAI,CAAAL,WAAW,CAAE,CAC9BxB,UAAU,CAAC4B,IAAI,CAAC,GAAGN,eAAe,eAAeO,IAAI,EAAE,CAAC,CACxD7B,UAAU,CAAC4B,IAAI,CAAC,GAAGN,eAAe,eAAeO,IAAI,EAAE,CAAC,CAC1D,CACF,CAEA;AACA,MAAO,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAC9B,UAAU,CAAC,CAAC,CACjC,CAEA;AACF;AACA;AACA,KACE,KAAM,CAAAO,eAAeA,CAACD,GAAG,CAAE,CACzB,GAAI,CACF,KAAM,CAAAyB,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAC,SAAS,CAAGC,UAAU,CAAC,IAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,CAAE,IAAI,CAAC,CAAE;AAE9D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,GAAG/B,GAAG,GAAG,CAAE,CACtCgC,MAAM,CAAE,KAAK,CACbC,MAAM,CAAER,UAAU,CAACQ,MAAM,CACzBC,OAAO,CAAE,CACP,QAAQ,CAAE,kBACZ,CACF,CAAC,CAAC,CAEFC,YAAY,CAACR,SAAS,CAAC,CAEvB,GAAIG,QAAQ,CAACM,EAAE,CAAE,CACf,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAP,QAAQ,CAACQ,IAAI,CAAC,CAAC,CAClC;AACA,GAAID,IAAI,EAAIA,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACE,OAAO,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CACzD,MAAO,KAAI,CACb,CACF,CAEA,MAAO,MAAK,CACd,CAAE,MAAO7B,KAAK,CAAE,CACd;AACA,MAAO,MAAK,CACd,CACF,CAEA;AACF;AACA,KACE,KAAM,CAAA8B,SAASA,CAAA,CAAgB,IAAf,CAAAC,QAAQ,CAAAC,SAAA,CAAArC,MAAA,IAAAqC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,EAAE,CAC3B,KAAM,CAAAtD,OAAO,CAAG,KAAM,KAAI,CAACG,kBAAkB,CAAC,CAAC,CAC/C,MAAO,GAAGH,OAAO,GAAGqD,QAAQ,EAAE,CAChC,CAEA;AACF;AACA,KACEG,KAAKA,CAAA,CAAG,CACN,IAAI,CAACxD,OAAO,CAAG,IAAI,CACnB,IAAI,CAACC,aAAa,CAAG,KAAK,CAC1B,IAAI,CAACC,gBAAgB,CAAG,IAAI,CAC9B,CAEA;AACF;AACA,KACEuD,aAAaA,CAAC9C,GAAG,CAAE,CACjB,IAAI,CAACX,OAAO,CAAGW,GAAG,CACpB,CACF,CAEA;AACA,KAAM,CAAA+C,SAAS,CAAG,GAAI,CAAA5D,SAAS,CAAC,CAAC,CAEjC,cAAe,CAAA4D,SAAS,CAExB;AACA,MAAO,MAAM,CAAAN,SAAS,CAAG,QAAAA,CAAA,KAAC,CAAAC,QAAQ,CAAAC,SAAA,CAAArC,MAAA,IAAAqC,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,EAAE,OAAK,CAAAI,SAAS,CAACN,SAAS,CAACC,QAAQ,CAAC,GACzE,MAAO,MAAM,CAAAM,cAAc,CAAGA,CAAA,GAAMD,SAAS,CAACF,KAAK,CAAC,CAAC,CACrD,MAAO,MAAM,CAAAC,aAAa,CAAI9C,GAAG,EAAK+C,SAAS,CAACD,aAAa,CAAC9C,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}