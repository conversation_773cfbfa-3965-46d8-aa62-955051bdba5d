{"ast": null, "code": "/**\n * API配置管理模块 - 正确版本\n */\n\nclass ApiConfig {\n  constructor() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n  async discoverBackendUrl() {\n    if (this.baseUrl) {\n      return this.baseUrl;\n    }\n    if (this.isDiscovering) {\n      return this.discoveryPromise;\n    }\n    this.isDiscovering = true;\n    this.discoveryPromise = this._performDiscovery();\n    try {\n      this.baseUrl = await this.discoveryPromise;\n      return this.baseUrl;\n    } finally {\n      this.isDiscovering = false;\n    }\n  }\n  async _performDiscovery() {\n    const candidates = this._getCandidateUrls();\n    console.log('🔍 开始自动发现后端服务...', candidates);\n\n    // 顺序测试候选地址（避免并发问题）\n    for (const url of candidates) {\n      try {\n        const isValid = await this._testBackendUrl(url);\n        if (isValid) {\n          console.log('✅ 发现后端服务:', url);\n          return url;\n        }\n      } catch (error) {\n        console.log(`❌ 测试失败 ${url}:`, error.message);\n      }\n    }\n    throw new Error('无法发现后端服务，请确保后端服务正在运行');\n  }\n  _getCandidateUrls() {\n    const candidates = [];\n\n    // 1. 环境变量配置的地址（优先级最高）\n    if (process.env.REACT_APP_API_URL) {\n      candidates.push(process.env.REACT_APP_API_URL);\n    }\n\n    // 2. 常见的后端地址\n    const commonUrls = ['http://localhost:8001', 'http://127.0.0.1:8001', 'http://localhost:8000', 'http://127.0.0.1:8000', 'http://localhost:5000', 'http://127.0.0.1:5000', 'http://localhost:3001', 'http://127.0.0.1:3001', 'http://localhost:8080', 'http://127.0.0.1:8080'];\n    candidates.push(...commonUrls);\n\n    // 去重\n    return [...new Set(candidates)];\n  }\n  async _testBackendUrl(url) {\n    try {\n      console.log(`🔍 测试地址: ${url}`);\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 3000);\n      const response = await fetch(`${url}/`, {\n        method: 'GET',\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json'\n        }\n      });\n      clearTimeout(timeoutId);\n      if (response.ok) {\n        const data = await response.json();\n        console.log(`✅ 响应成功: ${url}`, data);\n\n        // 验证响应是否包含预期的字段\n        if (data && data.message && data.message.includes('图像增强')) {\n          return true;\n        } else {\n          console.log(`❌ 响应格式不正确: ${url}`);\n        }\n      } else {\n        console.log(`❌ 响应状态错误: ${url} - ${response.status}`);\n      }\n      return false;\n    } catch (error) {\n      console.log(`❌ 请求失败: ${url} - ${error.message}`);\n      return false;\n    }\n  }\n  async getApiUrl(endpoint = '') {\n    const baseUrl = await this.discoverBackendUrl();\n    return `${baseUrl}${endpoint}`;\n  }\n  reset() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n  setBackendUrl(url) {\n    this.baseUrl = url;\n  }\n}\n\n// 创建全局实例\nconst apiConfig = new ApiConfig();\nexport default apiConfig;\n\n// 便捷方法\nexport const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);\nexport const resetApiConfig = () => apiConfig.reset();\nexport const setBackendUrl = url => apiConfig.setBackendUrl(url);", "map": {"version": 3, "names": ["ApiConfig", "constructor", "baseUrl", "isDiscovering", "discoveryPromise", "discoverBackendUrl", "_performDiscovery", "candidates", "_getCandidateUrls", "console", "log", "url", "<PERSON><PERSON><PERSON><PERSON>", "_testBackendUrl", "error", "message", "Error", "process", "env", "REACT_APP_API_URL", "push", "commonUrls", "Set", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "method", "signal", "headers", "clearTimeout", "ok", "data", "json", "includes", "status", "getApiUrl", "endpoint", "reset", "setBackendUrl", "apiConfig", "resetApiConfig"], "sources": ["/Users/<USER>/code/imageenhanceproweb/frontend/src/config/api.js"], "sourcesContent": ["/**\n * API配置管理模块 - 正确版本\n */\n\nclass ApiConfig {\n  constructor() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  async discoverBackendUrl() {\n    if (this.baseUrl) {\n      return this.baseUrl;\n    }\n\n    if (this.isDiscovering) {\n      return this.discoveryPromise;\n    }\n\n    this.isDiscovering = true;\n    this.discoveryPromise = this._performDiscovery();\n\n    try {\n      this.baseUrl = await this.discoveryPromise;\n      return this.baseUrl;\n    } finally {\n      this.isDiscovering = false;\n    }\n  }\n\n  async _performDiscovery() {\n    const candidates = this._getCandidateUrls();\n    \n    console.log('🔍 开始自动发现后端服务...', candidates);\n\n    // 顺序测试候选地址（避免并发问题）\n    for (const url of candidates) {\n      try {\n        const isValid = await this._testBackendUrl(url);\n        if (isValid) {\n          console.log('✅ 发现后端服务:', url);\n          return url;\n        }\n      } catch (error) {\n        console.log(`❌ 测试失败 ${url}:`, error.message);\n      }\n    }\n    \n    throw new Error('无法发现后端服务，请确保后端服务正在运行');\n  }\n\n  _getCandidateUrls() {\n    const candidates = [];\n    \n    // 1. 环境变量配置的地址（优先级最高）\n    if (process.env.REACT_APP_API_URL) {\n      candidates.push(process.env.REACT_APP_API_URL);\n    }\n    \n    // 2. 常见的后端地址\n    const commonUrls = [\n      'http://localhost:8001',\n      'http://127.0.0.1:8001',\n      'http://localhost:8000',\n      'http://127.0.0.1:8000',\n      'http://localhost:5000',\n      'http://127.0.0.1:5000',\n      'http://localhost:3001',\n      'http://127.0.0.1:3001',\n      'http://localhost:8080',\n      'http://127.0.0.1:8080'\n    ];\n    \n    candidates.push(...commonUrls);\n    \n    // 去重\n    return [...new Set(candidates)];\n  }\n\n  async _testBackendUrl(url) {\n    try {\n      console.log(`🔍 测试地址: ${url}`);\n      \n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 3000);\n      \n      const response = await fetch(`${url}/`, {\n        method: 'GET',\n        signal: controller.signal,\n        headers: {\n          'Accept': 'application/json',\n        }\n      });\n      \n      clearTimeout(timeoutId);\n      \n      if (response.ok) {\n        const data = await response.json();\n        console.log(`✅ 响应成功: ${url}`, data);\n        \n        // 验证响应是否包含预期的字段\n        if (data && data.message && data.message.includes('图像增强')) {\n          return true;\n        } else {\n          console.log(`❌ 响应格式不正确: ${url}`);\n        }\n      } else {\n        console.log(`❌ 响应状态错误: ${url} - ${response.status}`);\n      }\n      \n      return false;\n    } catch (error) {\n      console.log(`❌ 请求失败: ${url} - ${error.message}`);\n      return false;\n    }\n  }\n\n  async getApiUrl(endpoint = '') {\n    const baseUrl = await this.discoverBackendUrl();\n    return `${baseUrl}${endpoint}`;\n  }\n\n  reset() {\n    this.baseUrl = null;\n    this.isDiscovering = false;\n    this.discoveryPromise = null;\n  }\n\n  setBackendUrl(url) {\n    this.baseUrl = url;\n  }\n}\n\n// 创建全局实例\nconst apiConfig = new ApiConfig();\n\nexport default apiConfig;\n\n// 便捷方法\nexport const getApiUrl = (endpoint = '') => apiConfig.getApiUrl(endpoint);\nexport const resetApiConfig = () => apiConfig.reset();\nexport const setBackendUrl = (url) => apiConfig.setBackendUrl(url);\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,SAAS,CAAC;EACdC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAEA,MAAMC,kBAAkBA,CAAA,EAAG;IACzB,IAAI,IAAI,CAACH,OAAO,EAAE;MAChB,OAAO,IAAI,CAACA,OAAO;IACrB;IAEA,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB,OAAO,IAAI,CAACC,gBAAgB;IAC9B;IAEA,IAAI,CAACD,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,gBAAgB,GAAG,IAAI,CAACE,iBAAiB,CAAC,CAAC;IAEhD,IAAI;MACF,IAAI,CAACJ,OAAO,GAAG,MAAM,IAAI,CAACE,gBAAgB;MAC1C,OAAO,IAAI,CAACF,OAAO;IACrB,CAAC,SAAS;MACR,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B;EACF;EAEA,MAAMG,iBAAiBA,CAAA,EAAG;IACxB,MAAMC,UAAU,GAAG,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAE3CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEH,UAAU,CAAC;;IAE3C;IACA,KAAK,MAAMI,GAAG,IAAIJ,UAAU,EAAE;MAC5B,IAAI;QACF,MAAMK,OAAO,GAAG,MAAM,IAAI,CAACC,eAAe,CAACF,GAAG,CAAC;QAC/C,IAAIC,OAAO,EAAE;UACXH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,GAAG,CAAC;UAC7B,OAAOA,GAAG;QACZ;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdL,OAAO,CAACC,GAAG,CAAC,UAAUC,GAAG,GAAG,EAAEG,KAAK,CAACC,OAAO,CAAC;MAC9C;IACF;IAEA,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;EACzC;EAEAR,iBAAiBA,CAAA,EAAG;IAClB,MAAMD,UAAU,GAAG,EAAE;;IAErB;IACA,IAAIU,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;MACjCZ,UAAU,CAACa,IAAI,CAACH,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;IAChD;;IAEA;IACA,MAAME,UAAU,GAAG,CACjB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,EACvB,uBAAuB,CACxB;IAEDd,UAAU,CAACa,IAAI,CAAC,GAAGC,UAAU,CAAC;;IAE9B;IACA,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACf,UAAU,CAAC,CAAC;EACjC;EAEA,MAAMM,eAAeA,CAACF,GAAG,EAAE;IACzB,IAAI;MACFF,OAAO,CAACC,GAAG,CAAC,YAAYC,GAAG,EAAE,CAAC;MAE9B,MAAMY,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;MACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC;MAE5D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGlB,GAAG,GAAG,EAAE;QACtCmB,MAAM,EAAE,KAAK;QACbC,MAAM,EAAER,UAAU,CAACQ,MAAM;QACzBC,OAAO,EAAE;UACP,QAAQ,EAAE;QACZ;MACF,CAAC,CAAC;MAEFC,YAAY,CAACR,SAAS,CAAC;MAEvB,IAAIG,QAAQ,CAACM,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;QAClC3B,OAAO,CAACC,GAAG,CAAC,WAAWC,GAAG,EAAE,EAAEwB,IAAI,CAAC;;QAEnC;QACA,IAAIA,IAAI,IAAIA,IAAI,CAACpB,OAAO,IAAIoB,IAAI,CAACpB,OAAO,CAACsB,QAAQ,CAAC,MAAM,CAAC,EAAE;UACzD,OAAO,IAAI;QACb,CAAC,MAAM;UACL5B,OAAO,CAACC,GAAG,CAAC,cAAcC,GAAG,EAAE,CAAC;QAClC;MACF,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,aAAaC,GAAG,MAAMiB,QAAQ,CAACU,MAAM,EAAE,CAAC;MACtD;MAEA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOxB,KAAK,EAAE;MACdL,OAAO,CAACC,GAAG,CAAC,WAAWC,GAAG,MAAMG,KAAK,CAACC,OAAO,EAAE,CAAC;MAChD,OAAO,KAAK;IACd;EACF;EAEA,MAAMwB,SAASA,CAACC,QAAQ,GAAG,EAAE,EAAE;IAC7B,MAAMtC,OAAO,GAAG,MAAM,IAAI,CAACG,kBAAkB,CAAC,CAAC;IAC/C,OAAO,GAAGH,OAAO,GAAGsC,QAAQ,EAAE;EAChC;EAEAC,KAAKA,CAAA,EAAG;IACN,IAAI,CAACvC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAEAsC,aAAaA,CAAC/B,GAAG,EAAE;IACjB,IAAI,CAACT,OAAO,GAAGS,GAAG;EACpB;AACF;;AAEA;AACA,MAAMgC,SAAS,GAAG,IAAI3C,SAAS,CAAC,CAAC;AAEjC,eAAe2C,SAAS;;AAExB;AACA,OAAO,MAAMJ,SAAS,GAAGA,CAACC,QAAQ,GAAG,EAAE,KAAKG,SAAS,CAACJ,SAAS,CAACC,QAAQ,CAAC;AACzE,OAAO,MAAMI,cAAc,GAAGA,CAAA,KAAMD,SAAS,CAACF,KAAK,CAAC,CAAC;AACrD,OAAO,MAAMC,aAAa,GAAI/B,GAAG,IAAKgC,SAAS,CAACD,aAAa,CAAC/B,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}