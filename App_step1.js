import React, { useState, useRef } from 'react';
// import UploadForm from './UploadForm';
// import ResultView from './ResultView';
import { getApiUrl } from './config/api';

function App() {
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [originalImage, setOriginalImage] = useState(null);
  const uploadFormRef = useRef(null);

  const handleUpload = async (formData) => {
    setIsLoading(true);
    setError(null);
    
    try {
      // 使用自动发现的API地址
      const apiUrl = await getApiUrl('/enhance/');
      console.log('发现的API地址:', apiUrl);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '处理失败');
      }

      const data = await response.json();
      setResult(data);
    } catch (err) {
      console.error('上传失败:', err);
      setError(err.message || '网络错误，请检查后端服务是否正常运行');
    } finally {
      setIsLoading(false);
    }
  };

  const testApiDiscovery = async () => {
    try {
      const apiUrl = await getApiUrl('/');
      alert(`API发现成功: ${apiUrl}`);
    } catch (error) {
      alert(`API发现失败: ${error.message}`);
    }
  };

  return (
    <div style={{ 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1>图像增强应用 - 步骤1测试</h1>
      <p>测试API发现功能和基础组件</p>
      
      <div style={{
        backgroundColor: '#f0f0f0',
        padding: '15px',
        borderRadius: '5px',
        margin: '20px 0'
      }}>
        <h3>状态信息:</h3>
        <ul>
          <li>加载状态: {isLoading ? '是' : '否'}</li>
          <li>错误信息: {error || '无'}</li>
          <li>结果: {result ? '有数据' : '无数据'}</li>
        </ul>
      </div>
      
      <button 
        onClick={testApiDiscovery}
        style={{
          padding: '10px 20px',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginRight: '10px'
        }}
      >
        测试API发现
      </button>
      
      {error && (
        <div style={{
          padding: '12px',
          backgroundColor: '#ff4444',
          color: '#fff',
          margin: '8px 0',
          borderRadius: '4px',
          fontSize: '14px'
        }}>
          {error}
        </div>
      )}
    </div>
  );
}

export default App;
