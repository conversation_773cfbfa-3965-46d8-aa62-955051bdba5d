<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API自动发现功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 API自动发现功能测试</h1>
        <p>此页面用于测试前端自动发现后端服务地址的功能。</p>
        
        <div>
            <button onclick="testApiDiscovery()" id="testBtn">开始测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        // 简化版的API发现逻辑（用于测试）
        class SimpleApiConfig {
            constructor() {
                this.baseUrl = null;
            }

            async discoverBackendUrl() {
                if (this.baseUrl) {
                    return this.baseUrl;
                }

                const candidates = this._getCandidateUrls();
                logInfo(`🔍 开始测试候选地址: ${candidates.join(', ')}`);

                for (const url of candidates) {
                    try {
                        logInfo(`测试地址: ${url}`);
                        const isValid = await this._testBackendUrl(url);
                        if (isValid) {
                            this.baseUrl = url;
                            logSuccess(`✅ 发现后端服务: ${url}`);
                            return url;
                        } else {
                            logError(`❌ 地址无效: ${url}`);
                        }
                    } catch (error) {
                        logError(`❌ 测试失败 ${url}: ${error.message}`);
                    }
                }

                throw new Error('无法发现后端服务');
            }

            _getCandidateUrls() {
                const currentHost = window.location.hostname;
                const currentProtocol = window.location.protocol;
                const commonPorts = [8001, 8000, 5000, 3001, 8080];
                
                const candidates = [];
                
                // 同主机的常见端口
                for (const port of commonPorts) {
                    candidates.push(`${currentProtocol}//${currentHost}:${port}`);
                }
                
                return candidates;
            }

            async _testBackendUrl(url) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 3000);
                    
                    const response = await fetch(`${url}/`, {
                        method: 'GET',
                        signal: controller.signal,
                        headers: {
                            'Accept': 'application/json',
                        }
                    });
                    
                    clearTimeout(timeoutId);
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data && data.message && data.message.includes('图像增强')) {
                            return true;
                        }
                    }
                    
                    return false;
                } catch (error) {
                    return false;
                }
            }
        }

        const apiConfig = new SimpleApiConfig();

        function logSuccess(message) {
            const div = document.createElement('div');
            div.className = 'test-result success';
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function logError(message) {
            const div = document.createElement('div');
            div.className = 'test-result error';
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function logInfo(message) {
            const div = document.createElement('div');
            div.className = 'test-result info';
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        async function testApiDiscovery() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';
            
            clearResults();
            logInfo('🧪 开始API自动发现测试...');

            try {
                // 重置配置
                apiConfig.baseUrl = null;
                
                // 执行发现
                const baseUrl = await apiConfig.discoverBackendUrl();
                
                // 测试API响应
                logInfo('📡 测试API响应...');
                const response = await fetch(baseUrl);
                const data = await response.json();
                logSuccess(`✅ API响应正常: ${data.message}`);
                
                logSuccess('🎉 API自动发现功能测试通过！');
                
            } catch (error) {
                logError(`❌ 测试失败: ${error.message}`);
            } finally {
                btn.disabled = false;
                btn.textContent = '开始测试';
            }
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
